"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/PlannerNode.tsx":
/*!***********************************************************!*\
  !*** ./src/components/manual-build/nodes/PlannerNode.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlannerNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PlannerNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const maxSubtasks = (config === null || config === void 0 ? void 0 : config.maxSubtasks) || 10;\n    const getProviderName = (id)=>{\n        const providerMap = {\n            'openai': 'OpenAI',\n            'anthropic': 'Anthropic',\n            'google': 'Google',\n            'groq': 'Groq',\n            'deepseek': 'DeepSeek',\n            'openrouter': 'OpenRouter'\n        };\n        return providerMap[id] || id;\n    };\n    const getModelDisplayName = (modelId)=>{\n        if (!modelId) return '';\n        // Extract model name from ID (e.g., 'gpt-4' from 'openai/gpt-4')\n        const parts = modelId.split('/');\n        return parts[parts.length - 1];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#8b5cf6\",\n        hasInput: false,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                providerId && modelId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: getProviderName(providerId)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: getModelDisplayName(modelId)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Max subtasks: \",\n                                maxSubtasks\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this),\n                        (config === null || config === void 0 ? void 0 : config.temperature) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                \"Temperature: \",\n                                config.temperature\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-400\",\n                    children: \"Configure AI model for planning\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-purple-300 bg-purple-900/30 px-2 py-1 rounded\",\n                    children: \"\\uD83D\\uDCCB Planning Agent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\PlannerNode.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c = PlannerNode;\nvar _c;\n$RefreshReg$(_c, \"PlannerNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hbnVhbC1idWlsZC9ub2Rlcy9QbGFubmVyTm9kZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMEQ7QUFFeEI7QUFHbkIsU0FBU0UsWUFBWSxLQUF5QztRQUF6QyxFQUFFQyxJQUFJLEVBQW1DLEdBQXpDO0lBQ2xDLE1BQU1DLFNBQVNELEtBQUtDLE1BQU07SUFDMUIsTUFBTUMsYUFBYUQsbUJBQUFBLDZCQUFBQSxPQUFRQyxVQUFVO0lBQ3JDLE1BQU1DLFVBQVVGLG1CQUFBQSw2QkFBQUEsT0FBUUUsT0FBTztJQUMvQixNQUFNQyxjQUFjSCxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFHLFdBQVcsS0FBSTtJQUUzQyxNQUFNQyxrQkFBa0IsQ0FBQ0M7UUFDdkIsTUFBTUMsY0FBc0M7WUFDMUMsVUFBVTtZQUNWLGFBQWE7WUFDYixVQUFVO1lBQ1YsUUFBUTtZQUNSLFlBQVk7WUFDWixjQUFjO1FBQ2hCO1FBQ0EsT0FBT0EsV0FBVyxDQUFDRCxHQUFHLElBQUlBO0lBQzVCO0lBRUEsTUFBTUUsc0JBQXNCLENBQUNMO1FBQzNCLElBQUksQ0FBQ0EsU0FBUyxPQUFPO1FBQ3JCLGlFQUFpRTtRQUNqRSxNQUFNTSxRQUFRTixRQUFRTyxLQUFLLENBQUM7UUFDNUIsT0FBT0QsS0FBSyxDQUFDQSxNQUFNRSxNQUFNLEdBQUcsRUFBRTtJQUNoQztJQUVBLHFCQUNFLDhEQUFDYixpREFBUUE7UUFDUEUsTUFBTUE7UUFDTlksTUFBTWYscUdBQVdBO1FBQ2pCZ0IsT0FBTTtRQUNOQyxVQUFVO1FBQ1ZDLFdBQVc7a0JBRVgsNEVBQUNDO1lBQUlDLFdBQVU7O2dCQUNaZixjQUFjQyx3QkFDYiw4REFBQ2E7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFLRCxXQUFVOzhDQUNiWixnQkFBZ0JIOzs7Ozs7OENBRW5CLDhEQUFDZ0I7b0NBQUtELFdBQVU7OENBQ2JULG9CQUFvQkw7Ozs7Ozs7Ozs7OztzQ0FJekIsOERBQUNhOzRCQUFJQyxXQUFVOztnQ0FBd0I7Z0NBQ3RCYjs7Ozs7Ozt3QkFHaEJILENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUWtCLFdBQVcsTUFBS0MsMkJBQ3ZCLDhEQUFDSjs0QkFBSUMsV0FBVTs7Z0NBQXdCO2dDQUN2QmhCLE9BQU9rQixXQUFXOzs7Ozs7Ozs7Ozs7eUNBT3RDLDhEQUFDSDtvQkFBSUMsV0FBVTs4QkFBd0I7Ozs7Ozs4QkFLekMsOERBQUNEO29CQUFJQyxXQUFVOzhCQUE2RDs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNcEY7S0FyRXdCbEIiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxtYW51YWwtYnVpbGRcXG5vZGVzXFxQbGFubmVyTm9kZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBDcHVDaGlwSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBOb2RlUHJvcHMgfSBmcm9tICdAeHlmbG93L3JlYWN0JztcbmltcG9ydCBCYXNlTm9kZSBmcm9tICcuL0Jhc2VOb2RlJztcbmltcG9ydCB7IFdvcmtmbG93Tm9kZSwgUGxhbm5lck5vZGVEYXRhIH0gZnJvbSAnQC90eXBlcy9tYW51YWxCdWlsZCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBsYW5uZXJOb2RlKHsgZGF0YSB9OiBOb2RlUHJvcHM8V29ya2Zsb3dOb2RlWydkYXRhJ10+KSB7XG4gIGNvbnN0IGNvbmZpZyA9IGRhdGEuY29uZmlnIGFzIFBsYW5uZXJOb2RlRGF0YVsnY29uZmlnJ107XG4gIGNvbnN0IHByb3ZpZGVySWQgPSBjb25maWc/LnByb3ZpZGVySWQ7XG4gIGNvbnN0IG1vZGVsSWQgPSBjb25maWc/Lm1vZGVsSWQ7XG4gIGNvbnN0IG1heFN1YnRhc2tzID0gY29uZmlnPy5tYXhTdWJ0YXNrcyB8fCAxMDtcblxuICBjb25zdCBnZXRQcm92aWRlck5hbWUgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHByb3ZpZGVyTWFwOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAgICAgJ29wZW5haSc6ICdPcGVuQUknLFxuICAgICAgJ2FudGhyb3BpYyc6ICdBbnRocm9waWMnLFxuICAgICAgJ2dvb2dsZSc6ICdHb29nbGUnLFxuICAgICAgJ2dyb3EnOiAnR3JvcScsXG4gICAgICAnZGVlcHNlZWsnOiAnRGVlcFNlZWsnLFxuICAgICAgJ29wZW5yb3V0ZXInOiAnT3BlblJvdXRlcidcbiAgICB9O1xuICAgIHJldHVybiBwcm92aWRlck1hcFtpZF0gfHwgaWQ7XG4gIH07XG5cbiAgY29uc3QgZ2V0TW9kZWxEaXNwbGF5TmFtZSA9IChtb2RlbElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIW1vZGVsSWQpIHJldHVybiAnJztcbiAgICAvLyBFeHRyYWN0IG1vZGVsIG5hbWUgZnJvbSBJRCAoZS5nLiwgJ2dwdC00JyBmcm9tICdvcGVuYWkvZ3B0LTQnKVxuICAgIGNvbnN0IHBhcnRzID0gbW9kZWxJZC5zcGxpdCgnLycpO1xuICAgIHJldHVybiBwYXJ0c1twYXJ0cy5sZW5ndGggLSAxXTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxCYXNlTm9kZVxuICAgICAgZGF0YT17ZGF0YX1cbiAgICAgIGljb249e0NwdUNoaXBJY29ufVxuICAgICAgY29sb3I9XCIjOGI1Y2Y2XCJcbiAgICAgIGhhc0lucHV0PXtmYWxzZX0gLy8gUGxhbm5lciBoYXMgbm8gaW5wdXQgLSBpdCdzIGEgc3RhcnRpbmcgbm9kZVxuICAgICAgaGFzT3V0cHV0PXt0cnVlfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIHtwcm92aWRlcklkICYmIG1vZGVsSWQgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAge2dldFByb3ZpZGVyTmFtZShwcm92aWRlcklkKX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICB7Z2V0TW9kZWxEaXNwbGF5TmFtZShtb2RlbElkKX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgIE1heCBzdWJ0YXNrczoge21heFN1YnRhc2tzfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHtjb25maWc/LnRlbXBlcmF0dXJlICE9PSB1bmRlZmluZWQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIFRlbXBlcmF0dXJlOiB7Y29uZmlnLnRlbXBlcmF0dXJlfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cblxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICBDb25maWd1cmUgQUkgbW9kZWwgZm9yIHBsYW5uaW5nXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcHVycGxlLTMwMCBiZy1wdXJwbGUtOTAwLzMwIHB4LTIgcHktMSByb3VuZGVkXCI+XG4gICAgICAgICAg8J+TiyBQbGFubmluZyBBZ2VudFxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvQmFzZU5vZGU+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQ3B1Q2hpcEljb24iLCJCYXNlTm9kZSIsIlBsYW5uZXJOb2RlIiwiZGF0YSIsImNvbmZpZyIsInByb3ZpZGVySWQiLCJtb2RlbElkIiwibWF4U3VidGFza3MiLCJnZXRQcm92aWRlck5hbWUiLCJpZCIsInByb3ZpZGVyTWFwIiwiZ2V0TW9kZWxEaXNwbGF5TmFtZSIsInBhcnRzIiwic3BsaXQiLCJsZW5ndGgiLCJpY29uIiwiY29sb3IiLCJoYXNJbnB1dCIsImhhc091dHB1dCIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJ0ZW1wZXJhdHVyZSIsInVuZGVmaW5lZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/PlannerNode.tsx\n"));

/***/ })

});