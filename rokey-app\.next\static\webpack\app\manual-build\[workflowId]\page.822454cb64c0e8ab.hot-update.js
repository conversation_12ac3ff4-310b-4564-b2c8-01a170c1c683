"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Role management state\n    const [customRoles, setCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingRoles, setIsLoadingRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rolesError, setRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Fetch custom roles from database\n    const fetchCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchCustomRoles]\": async ()=>{\n            setIsLoadingRoles(true);\n            setRolesError(null);\n            try {\n                const response = await fetch('/api/user/custom-roles');\n                if (!response.ok) {\n                    throw new Error('Failed to fetch custom roles');\n                }\n                const roles = await response.json();\n                setCustomRoles(roles);\n            } catch (err) {\n                console.error('Error fetching custom roles:', err);\n                setRolesError(err.message);\n                setCustomRoles([]);\n            } finally{\n                setIsLoadingRoles(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchCustomRoles]\"], []);\n    // Load models and roles on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' || node.type === 'vision' || node.type === 'planner') {\n                fetchModelsFromDatabase();\n            }\n            if (node.type === 'roleAgent') {\n                fetchCustomRoles();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase,\n        fetchCustomRoles\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if ((node.type === 'provider' || node.type === 'vision' || node.type === 'planner') && fetchedProviderModels && fetchedProviderModels.length > 0) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {\n                    let availableModels = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels.length > 0) {\n                        const selectedModelId = availableModels[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                        const currentParams = providerConfig.parameters || {};\n                        // Update config in a single call to avoid infinite loops\n                        const newConfig = {\n                            ...providerConfig,\n                            modelId: selectedModelId,\n                            parameters: {\n                                ...currentParams,\n                                maxTokens: currentParams.maxTokens || reasonableDefault\n                            }\n                        };\n                        setConfig(newConfig);\n                        onUpdate({\n                            config: newConfig,\n                            isConfigured: isNodeConfigured(node.type, newConfig)\n                        });\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        node.type,\n        config === null || config === void 0 ? void 0 : config.providerId\n    ]); // Only re-run when provider changes\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value\n        };\n        // Only initialize parameters if they don't exist and we're setting a parameter\n        if (key === 'parameters' || !currentConfig.parameters) {\n            newConfig.parameters = {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined,\n                ...currentConfig.parameters,\n                ...key === 'parameters' ? value : {}\n            };\n        }\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision' || node.type === 'planner')) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // Filter function for vision nodes - only show multimodal models\n                const filterForVision = {\n                    \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (models)=>{\n                        if (node.type === 'vision') {\n                            return models.filter({\n                                \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (model)=>model.modality && (model.modality.includes('multimodal') || model.modality.includes('vision') || model.modality.includes('image'))\n                            }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"]);\n                        }\n                        return models;\n                    }\n                }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"];\n                // If the selected provider is \"OpenRouter\", show all fetched models (filtered for vision if needed)\n                if (currentProviderDetails.id === \"openrouter\") {\n                    const filteredModels = filterForVision(fetchedProviderModels);\n                    return filteredModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    var _deepseekChatModel_modality, _deepseekReasonerModel_modality;\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel && (node.type === 'provider' || node.type === 'planner' || node.type === 'vision' && ((_deepseekChatModel_modality = deepseekChatModel.modality) === null || _deepseekChatModel_modality === void 0 ? void 0 : _deepseekChatModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel && (node.type === 'provider' || node.type === 'planner' || node.type === 'vision' && ((_deepseekReasonerModel_modality = deepseekReasonerModel.modality) === null || _deepseekReasonerModel_modality === void 0 ? void 0 : _deepseekReasonerModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id and vision capabilities\n                const providerModels = fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions].providerModels\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions].providerModels\"]);\n                const filteredModels = filterForVision(providerModels);\n                return filteredModels.map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider' && node.type !== 'vision' && node.type !== 'planner') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            return {\n                maxTokens,\n                minTokens\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'vision':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'roleAgent':\n                if (nodeConfig.roleType === 'new') {\n                    return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);\n                }\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'centralRouter':\n                return !!nodeConfig.routingStrategy;\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'planner':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'browsing':\n                return true; // Browsing node is always configured with defaults\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1, _providerConfig_parameters2, _providerConfig_parameters3;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key for this provider\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Required: Enter your own API key for this AI provider (BYOK)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...providerConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters2 = providerConfig.parameters) === null || _providerConfig_parameters2 === void 0 ? void 0 : _providerConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters3 = providerConfig.parameters) === null || _providerConfig_parameters3 === void 0 ? void 0 : _providerConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, this);\n    };\n    const renderVisionConfig = ()=>{\n        var _visionConfig_parameters, _visionConfig_parameters1, _visionConfig_parameters2, _visionConfig_parameters3;\n        const visionConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key for this provider\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Required: Enter your own API key for this AI provider (BYOK)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Vision Model\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-purple-400 ml-1\",\n                                    children: \"(Multimodal Only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...visionConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Vision Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No vision models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 11\n                        }, this),\n                        modelOptions.length === 0 && (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) && !isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg\",\n                            children: \"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Temperature (0.0 - 2.0)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters = visionConfig.parameters) === null || _visionConfig_parameters === void 0 ? void 0 : _visionConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters1 = visionConfig.parameters) === null || _visionConfig_parameters1 === void 0 ? void 0 : _visionConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters2 = visionConfig.parameters) === null || _visionConfig_parameters2 === void 0 ? void 0 : _visionConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters3 = visionConfig.parameters) === null || _visionConfig_parameters3 === void 0 ? void 0 : _visionConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate for vision analysis.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 785,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 778,\n                    columnNumber: 9\n                }, this),\n                (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 font-medium mb-1\",\n                            children: \"\\uD83D\\uDC41️ Vision Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-purple-200\",\n                            children: \"Access to multimodal models from multiple providers for image analysis and vision tasks.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 597,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        const roleConfig = config;\n        // Combine predefined and custom roles for dropdown\n        const availableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>({\n                    id: role.id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'predefined'\n                })),\n            ...customRoles.map((role)=>({\n                    id: role.role_id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'custom'\n                }))\n        ];\n        const handleRoleSelectionChange = (value)=>{\n            if (value === 'create_new') {\n                // Switch to create new role mode\n                const newConfig = {\n                    ...roleConfig,\n                    roleType: 'new',\n                    roleId: '',\n                    roleName: '',\n                    newRoleName: '',\n                    newRoleDescription: '',\n                    customPrompt: ''\n                };\n                setConfig(newConfig);\n                onUpdate({\n                    config: newConfig,\n                    isConfigured: isNodeConfigured(node.type, newConfig)\n                });\n            } else {\n                // Select existing role\n                const selectedRole = availableRoles.find((role)=>role.id === value);\n                if (selectedRole) {\n                    const newConfig = {\n                        ...roleConfig,\n                        roleType: selectedRole.type,\n                        roleId: selectedRole.id,\n                        roleName: selectedRole.name,\n                        customPrompt: selectedRole.description || ''\n                    };\n                    setConfig(newConfig);\n                    onUpdate({\n                        config: newConfig,\n                        isConfigured: isNodeConfigured(node.type, newConfig)\n                    });\n                }\n            }\n        };\n        const handleNewRoleChange = (field, value)=>{\n            const newConfig = {\n                ...roleConfig,\n                [field]: value\n            };\n            setConfig(newConfig);\n            onUpdate({\n                config: newConfig,\n                isConfigured: isNodeConfigured(node.type, newConfig)\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Select Role\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 928,\n                            columnNumber: 11\n                        }, this),\n                        isLoadingRoles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400\",\n                            children: \"Loading roles...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' ? 'create_new' : (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) || '',\n                            onChange: (e)=>handleRoleSelectionChange(e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a role...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"System Roles\",\n                                    children: _config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.id,\n                                            children: role.name\n                                        }, role.id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 946,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                customRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Your Custom Roles\",\n                                    children: customRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.role_id,\n                                            children: role.name\n                                        }, role.role_id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Create New\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"create_new\",\n                                        children: \"+ Create New Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 13\n                        }, this),\n                        rolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error loading roles: \",\n                                rolesError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 927,\n                    columnNumber: 9\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) !== 'new' && (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-white mb-1\",\n                            children: roleConfig.roleName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 13\n                        }, this),\n                        roleConfig.customPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300\",\n                            children: roleConfig.customPrompt\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 984,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 979,\n                    columnNumber: 11\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"New Role Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleName || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleName', e.target.value),\n                                    placeholder: \"e.g., Data Analyst, Creative Writer\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 994,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Role Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleDescription || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleDescription', e.target.value),\n                                    placeholder: \"Brief description of this role's purpose\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Custom Instructions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: roleConfig.customPrompt || '',\n                                    onChange: (e)=>handleNewRoleChange('customPrompt', e.target.value),\n                                    placeholder: \"Enter detailed instructions for this role...\",\n                                    rows: 4,\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1024,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.memoryEnabled) || false,\n                                    onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                    className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1038,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-300\",\n                                    children: \"Enable memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1 ml-6\",\n                            children: \"Allow this role to remember context from previous interactions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1036,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 925,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1058,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1066,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1069,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1057,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1076,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1079,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1075,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1088,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1056,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1133,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1120,\n            columnNumber: 7\n        }, this);\n    };\n    const renderCentralRouterConfig = ()=>{\n        const routerConfig = config;\n        var _routerConfig_enableCaching, _routerConfig_debugMode;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Routing Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.routingStrategy) || 'smart',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    routingStrategy: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"smart\",\n                                    children: \"Smart Routing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"round_robin\",\n                                    children: \"Round Robin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"load_balanced\",\n                                    children: \"Load Balanced\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"priority\",\n                                    children: \"Priority Based\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"How the router selects between available AI providers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Retries\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"0\",\n                            max: \"10\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.maxRetries) || 3,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    maxRetries: parseInt(e.target.value) || 3\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Number of retry attempts on failure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (ms)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1000\",\n                            max: \"300000\",\n                            step: \"1000\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.timeout) || 30000,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    timeout: parseInt(e.target.value) || 30000\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Request timeout in milliseconds\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Enable Caching\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_enableCaching = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.enableCaching) !== null && _routerConfig_enableCaching !== void 0 ? _routerConfig_enableCaching : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            enableCaching: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Cache responses to improve performance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1237,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Debug Mode\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_debugMode = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.debugMode) !== null && _routerConfig_debugMode !== void 0 ? _routerConfig_debugMode : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            debugMode: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Enable detailed logging for debugging\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1264,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1152,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolConfig = ()=>{\n        var _toolOptions_find, _toolOptions_find1;\n        const toolConfig = config;\n        const toolOptions = [\n            {\n                value: '',\n                label: 'Select a tool...'\n            },\n            {\n                value: 'google_drive',\n                label: '📁 Google Drive',\n                description: 'Access and manage Google Drive files'\n            },\n            {\n                value: 'google_docs',\n                label: '📄 Google Docs',\n                description: 'Create and edit Google Documents'\n            },\n            {\n                value: 'google_sheets',\n                label: '📊 Google Sheets',\n                description: 'Work with Google Spreadsheets'\n            },\n            {\n                value: 'zapier',\n                label: '⚡ Zapier',\n                description: 'Connect with 5000+ apps via Zapier'\n            },\n            {\n                value: 'notion',\n                label: '📝 Notion',\n                description: 'Access Notion databases and pages'\n            },\n            {\n                value: 'calendar',\n                label: '📅 Calendar',\n                description: 'Manage calendar events and schedules'\n            },\n            {\n                value: 'gmail',\n                label: '📧 Gmail',\n                description: 'Send and manage emails'\n            },\n            {\n                value: 'youtube',\n                label: '📺 YouTube',\n                description: 'Access YouTube data and analytics'\n            },\n            {\n                value: 'supabase',\n                label: '🗄️ Supabase',\n                description: 'Direct database operations'\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Tool Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    toolType: e.target.value,\n                                    // Reset tool-specific config when changing tool type\n                                    toolConfig: {},\n                                    // All tools need authentication\n                                    connectionStatus: 'disconnected',\n                                    isAuthenticated: false\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: toolOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: option.value,\n                                    children: option.label\n                                }, option.value, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1337,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1316,\n                            columnNumber: 11\n                        }, this),\n                        (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: (_toolOptions_find = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find === void 0 ? void 0 : _toolOptions_find.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1312,\n                    columnNumber: 9\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-yellow-400\",\n                                    children: \"Authentication Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1354,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1352,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mb-2\",\n                                    children: [\n                                        (_toolOptions_find1 = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find1 === void 0 ? void 0 : _toolOptions_find1.label,\n                                        \" integration coming soon!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"This tool will require account linking and authentication.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1361,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1351,\n                    columnNumber: 11\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1371,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"5\",\n                            max: \"300\",\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1374,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum time to wait for the tool operation to complete\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1392,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1370,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1311,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPlannerConfig = ()=>{\n        var _plannerConfig_parameters, _plannerConfig_parameters1, _plannerConfig_parameters2, _plannerConfig_parameters3;\n        const plannerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 0.7,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openai\",\n                                    children: \"OpenAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"anthropic\",\n                                    children: \"Anthropic\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"google\",\n                                    children: \"Google\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"deepseek\",\n                                    children: \"DeepSeek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"xai\",\n                                    children: \"xAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openrouter\",\n                                    children: \"OpenRouter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1440,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1410,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1406,\n                    columnNumber: 9\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1446,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                let updatedConfig = {\n                                    ...plannerConfig,\n                                    modelId: selectedModelId\n                                };\n                                // Set reasonable default for maxTokens based on model limits\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1486,\n                                columnNumber: 17\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1489,\n                                        columnNumber: 19\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1491,\n                                            columnNumber: 21\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1497,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1449,\n                            columnNumber: 13\n                        }, this),\n                        isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Loading models...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1503,\n                            columnNumber: 15\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-400 mt-1\",\n                            children: fetchProviderModelsError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1506,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1445,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1513,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    apiKey: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter your API key for this provider\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1516,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Required: Enter your own API key for this AI provider (BYOK)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1534,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1512,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Max Tokens: \",\n                                        (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters = plannerConfig.parameters) === null || _plannerConfig_parameters === void 0 ? void 0 : _plannerConfig_parameters.maxTokens) || 'Auto'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1544,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters1 = plannerConfig.parameters) === null || _plannerConfig_parameters1 === void 0 ? void 0 : _plannerConfig_parameters1.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...plannerConfig,\n                                            parameters: {\n                                                ...plannerConfig.parameters,\n                                                maxTokens: parseInt(e.target.value)\n                                            }\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getCurrentModelLimits.minTokens\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1569,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getCurrentModelLimits.maxTokens\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1570,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1568,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1543,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Temperature: \",\n                                        (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters2 = plannerConfig.parameters) === null || _plannerConfig_parameters2 === void 0 ? void 0 : _plannerConfig_parameters2.temperature) || 0.7\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1576,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters3 = plannerConfig.parameters) === null || _plannerConfig_parameters3 === void 0 ? void 0 : _plannerConfig_parameters3.temperature) || 0.7,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...plannerConfig,\n                                            parameters: {\n                                                ...plannerConfig.parameters,\n                                                temperature: parseFloat(e.target.value)\n                                            }\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1579,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"0 (Focused)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1602,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"2 (Creative)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1603,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1601,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Subtasks\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1612,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"50\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.maxSubtasks) || 10,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    maxSubtasks: parseInt(e.target.value) || 10\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1615,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum number of subtasks the planner can create\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1633,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1611,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1405,\n            columnNumber: 7\n        }, this);\n    };\n    const renderBrowsingConfig = ()=>{\n        var _browsingConfig_searchEngines, _browsingConfig_searchEngines1;\n        const browsingConfig = config;\n        var _browsingConfig_enableScreenshots, _browsingConfig_enableFormFilling, _browsingConfig_enableCaptchaSolving, _browsingConfig_searchEngines_includes, _browsingConfig_searchEngines_includes1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-green-900/20 border border-green-700 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1648,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-green-400\",\n                                    children: \"Intelligent Browsing Agent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1649,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1647,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-300\",\n                            children: \"This node automatically plans and executes complex web browsing tasks using AI.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1651,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1646,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Sites to Visit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1657,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"20\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.maxSites) || 5,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    maxSites: parseInt(e.target.value) || 5\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1660,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1656,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout per Operation (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1681,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"10\",\n                            max: \"300\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1684,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1680,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300\",\n                            children: \"Capabilities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1705,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableScreenshots = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableScreenshots) !== null && _browsingConfig_enableScreenshots !== void 0 ? _browsingConfig_enableScreenshots : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableScreenshots: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1710,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCF8 Take Screenshots\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1726,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1709,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableFormFilling = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableFormFilling) !== null && _browsingConfig_enableFormFilling !== void 0 ? _browsingConfig_enableFormFilling : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableFormFilling: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1730,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCDD Fill Forms Automatically\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1746,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableCaptchaSolving = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableCaptchaSolving) !== null && _browsingConfig_enableCaptchaSolving !== void 0 ? _browsingConfig_enableCaptchaSolving : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableCaptchaSolving: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1750,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDD10 Attempt CAPTCHA Solving\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1766,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1749,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1704,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Search Engines\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines === void 0 ? void 0 : _browsingConfig_searchEngines.includes('google')) !== null && _browsingConfig_searchEngines_includes !== void 0 ? _browsingConfig_searchEngines_includes : true,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'google'),\n                                                    'google'\n                                                ] : currentEngines.filter((eng)=>eng !== 'google');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1776,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1797,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1775,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes1 = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines1 = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines1 === void 0 ? void 0 : _browsingConfig_searchEngines1.includes('bing')) !== null && _browsingConfig_searchEngines_includes1 !== void 0 ? _browsingConfig_searchEngines_includes1 : false,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'bing'),\n                                                    'bing'\n                                                ] : currentEngines.filter((eng)=>eng !== 'bing');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1800,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Bing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1821,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1799,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1774,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1770,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1645,\n            columnNumber: 7\n        }, this);\n    };\n    const renderMemoryConfig = ()=>{\n        const memoryConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1836,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-blue-300\",\n                                    children: \"Plug & Play Memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1837,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1835,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-blue-200/80\",\n                            children: \"This memory node automatically acts as a brain for any connected node. It handles storing, retrieving, session data, and persistent memory intelligently without manual configuration.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1839,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1834,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Memory Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1846,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryName) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...memoryConfig,\n                                    memoryName: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter a name for this memory (e.g., Browsing Memory, Router Memory)\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1849,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Give this memory a descriptive name for easy identification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1867,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1845,\n                    columnNumber: 9\n                }, this),\n                (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Max Storage Size (MB)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1875,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"1\",\n                                    max: \"100\",\n                                    value: Math.round(((memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.maxSize) || 10240) / 1024),\n                                    onChange: (e)=>{\n                                        const sizeInMB = parseInt(e.target.value) || 10;\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            maxSize: sizeInMB * 1024 // Convert to KB\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1878,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Maximum storage size limit (default: 10MB)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1897,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1874,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.encryption) !== false,\n                                            onChange: (e)=>{\n                                                const newConfig = {\n                                                    ...memoryConfig,\n                                                    encryption: e.target.checked\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1904,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-gray-300\",\n                                            children: \"Enable encryption (recommended)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1920,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1903,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1 ml-6\",\n                                    children: \"Encrypt stored data for security (enabled by default)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1922,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1902,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Description (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1928,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.description) || '',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            description: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    placeholder: \"Describe what this memory will be used for...\",\n                                    rows: 2,\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1931,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Optional description of this memory's purpose\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1948,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1927,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1833,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'vision':\n                return renderVisionConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'centralRouter':\n                return renderCentralRouterConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            case 'tool':\n                return renderToolConfig();\n            case 'planner':\n                return renderPlannerConfig();\n            case 'browsing':\n                return renderBrowsingConfig();\n            case 'memory':\n                return renderMemoryConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1989,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1988,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1992,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1995,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1991,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1987,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 2004,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2000,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1986,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2009,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2016,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2019,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2015,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2023,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2014,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 1984,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"3qPqzANwA5DE2PGqVNbp9Fhx4wo=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});