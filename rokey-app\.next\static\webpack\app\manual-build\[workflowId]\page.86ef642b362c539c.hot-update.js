"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Role plugin for AI providers (connect to role input)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'centralRouter',\n                label: 'Central Router',\n                description: 'Smart routing hub for multiple AI providers and vision models',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Central Router',\n                    config: {\n                        routingStrategy: 'smart',\n                        fallbackProvider: '',\n                        maxRetries: 3,\n                        timeout: 30000,\n                        enableCaching: true,\n                        debugMode: false\n                    },\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'planner',\n                label: 'Planner',\n                description: 'AI model that creates browsing strategies and todo lists',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Planner',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 0.7,\n                            maxTokens: 1000\n                        },\n                        maxSubtasks: 10\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    logic: {\n        label: 'Logic & Control',\n        description: 'Flow control and decision making',\n        nodes: [\n            {\n                type: 'conditional',\n                label: 'Conditional',\n                description: 'Branch workflow based on conditions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Conditional',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'merge',\n                label: 'Merge',\n                description: 'Combine multiple inputs',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Merge',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'switch',\n                label: 'Switch',\n                description: 'Route to different paths',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Switch',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'loop',\n                label: 'Loop',\n                description: 'Repeat operations',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Loop',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tools',\n                description: 'External tool integrations (Google Drive, Zapier, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tools',\n                    config: {\n                        toolType: '',\n                        toolConfig: {},\n                        timeout: 30,\n                        connectionStatus: 'disconnected',\n                        isAuthenticated: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data across workflow executions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {\n                        memoryType: '',\n                        storageKey: '',\n                        storageScope: 'workflow',\n                        dataFormat: 'text',\n                        maxSize: 1024,\n                        ttl: 3600,\n                        encryption: false,\n                        description: ''\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    browsing: {\n        label: 'Web Browsing',\n        description: 'Intelligent web browsing and automation',\n        nodes: [\n            {\n                type: 'browsing',\n                label: 'Browsing Agent',\n                description: 'Intelligent web browsing agent with multi-step automation',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Browsing Agent',\n                    config: {\n                        maxSites: 5,\n                        timeout: 30,\n                        enableScreenshots: true,\n                        enableFormFilling: true,\n                        enableCaptchaSolving: false,\n                        searchEngines: [\n                            'google'\n                        ],\n                        maxDepth: 2,\n                        respectRobots: true,\n                        enableJavaScript: true\n                    },\n                    isConfigured: true\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 359,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});