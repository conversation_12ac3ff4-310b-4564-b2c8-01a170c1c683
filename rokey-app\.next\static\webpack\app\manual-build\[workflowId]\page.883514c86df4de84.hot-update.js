"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Role management state\n    const [customRoles, setCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingRoles, setIsLoadingRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rolesError, setRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Fetch custom roles from database\n    const fetchCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchCustomRoles]\": async ()=>{\n            setIsLoadingRoles(true);\n            setRolesError(null);\n            try {\n                const response = await fetch('/api/user/custom-roles');\n                if (!response.ok) {\n                    throw new Error('Failed to fetch custom roles');\n                }\n                const roles = await response.json();\n                setCustomRoles(roles);\n            } catch (err) {\n                console.error('Error fetching custom roles:', err);\n                setRolesError(err.message);\n                setCustomRoles([]);\n            } finally{\n                setIsLoadingRoles(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchCustomRoles]\"], []);\n    // Load models and roles on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' || node.type === 'vision') {\n                fetchModelsFromDatabase();\n            }\n            if (node.type === 'roleAgent') {\n                fetchCustomRoles();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase,\n        fetchCustomRoles\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if ((node.type === 'provider' || node.type === 'vision') && fetchedProviderModels && fetchedProviderModels.length > 0) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {\n                    let availableModels = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels.length > 0) {\n                        const selectedModelId = availableModels[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                        const currentParams = providerConfig.parameters || {};\n                        // Update config in a single call to avoid infinite loops\n                        const newConfig = {\n                            ...providerConfig,\n                            modelId: selectedModelId,\n                            parameters: {\n                                ...currentParams,\n                                maxTokens: currentParams.maxTokens || reasonableDefault\n                            }\n                        };\n                        setConfig(newConfig);\n                        onUpdate({\n                            config: newConfig,\n                            isConfigured: isNodeConfigured(node.type, newConfig)\n                        });\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        node.type,\n        config === null || config === void 0 ? void 0 : config.providerId\n    ]); // Only re-run when provider changes\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value\n        };\n        // Only initialize parameters if they don't exist and we're setting a parameter\n        if (key === 'parameters' || !currentConfig.parameters) {\n            newConfig.parameters = {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined,\n                ...currentConfig.parameters,\n                ...key === 'parameters' ? value : {}\n            };\n        }\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision')) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // Filter function for vision nodes - only show multimodal models\n                const filterForVision = {\n                    \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (models)=>{\n                        if (node.type === 'vision') {\n                            return models.filter({\n                                \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (model)=>model.modality && (model.modality.includes('multimodal') || model.modality.includes('vision') || model.modality.includes('image'))\n                            }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"]);\n                        }\n                        return models;\n                    }\n                }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"];\n                // If the selected provider is \"OpenRouter\", show all fetched models (filtered for vision if needed)\n                if (currentProviderDetails.id === \"openrouter\") {\n                    const filteredModels = filterForVision(fetchedProviderModels);\n                    return filteredModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    var _deepseekChatModel_modality, _deepseekReasonerModel_modality;\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel && (node.type === 'provider' || node.type === 'vision' && ((_deepseekChatModel_modality = deepseekChatModel.modality) === null || _deepseekChatModel_modality === void 0 ? void 0 : _deepseekChatModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel && (node.type === 'provider' || node.type === 'vision' && ((_deepseekReasonerModel_modality = deepseekReasonerModel.modality) === null || _deepseekReasonerModel_modality === void 0 ? void 0 : _deepseekReasonerModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id and vision capabilities\n                const providerModels = fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions].providerModels\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions].providerModels\"]);\n                const filteredModels = filterForVision(providerModels);\n                return filteredModels.map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider' && node.type !== 'vision') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            return {\n                maxTokens,\n                minTokens\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'vision':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                if (nodeConfig.roleType === 'new') {\n                    return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);\n                }\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'centralRouter':\n                return !!nodeConfig.routingStrategy;\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'planner':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'browsing':\n                return true; // Browsing node is always configured with defaults\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1, _providerConfig_parameters2, _providerConfig_parameters3;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...providerConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters2 = providerConfig.parameters) === null || _providerConfig_parameters2 === void 0 ? void 0 : _providerConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters3 = providerConfig.parameters) === null || _providerConfig_parameters3 === void 0 ? void 0 : _providerConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, this);\n    };\n    const renderVisionConfig = ()=>{\n        var _visionConfig_parameters, _visionConfig_parameters1, _visionConfig_parameters2, _visionConfig_parameters3;\n        const visionConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Vision Model\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-purple-400 ml-1\",\n                                    children: \"(Multimodal Only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...visionConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Vision Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No vision models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 11\n                        }, this),\n                        modelOptions.length === 0 && (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) && !isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg\",\n                            children: \"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Temperature (0.0 - 2.0)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters = visionConfig.parameters) === null || _visionConfig_parameters === void 0 ? void 0 : _visionConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters1 = visionConfig.parameters) === null || _visionConfig_parameters1 === void 0 ? void 0 : _visionConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 720,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters2 = visionConfig.parameters) === null || _visionConfig_parameters2 === void 0 ? void 0 : _visionConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters3 = visionConfig.parameters) === null || _visionConfig_parameters3 === void 0 ? void 0 : _visionConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate for vision analysis.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 770,\n                    columnNumber: 9\n                }, this),\n                (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 font-medium mb-1\",\n                            children: \"\\uD83D\\uDC41️ Vision Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-purple-200\",\n                            children: \"Access to multimodal models from multiple providers for image analysis and vision tasks.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 839,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 837,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 593,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        const roleConfig = config;\n        // Combine predefined and custom roles for dropdown\n        const availableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>({\n                    id: role.id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'predefined'\n                })),\n            ...customRoles.map((role)=>({\n                    id: role.role_id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'custom'\n                }))\n        ];\n        const handleRoleSelectionChange = (value)=>{\n            if (value === 'create_new') {\n                // Switch to create new role mode\n                const newConfig = {\n                    ...roleConfig,\n                    roleType: 'new',\n                    roleId: '',\n                    roleName: '',\n                    newRoleName: '',\n                    newRoleDescription: '',\n                    customPrompt: ''\n                };\n                setConfig(newConfig);\n                onUpdate({\n                    config: newConfig,\n                    isConfigured: isNodeConfigured(node.type, newConfig)\n                });\n            } else {\n                // Select existing role\n                const selectedRole = availableRoles.find((role)=>role.id === value);\n                if (selectedRole) {\n                    const newConfig = {\n                        ...roleConfig,\n                        roleType: selectedRole.type,\n                        roleId: selectedRole.id,\n                        roleName: selectedRole.name,\n                        customPrompt: selectedRole.description || ''\n                    };\n                    setConfig(newConfig);\n                    onUpdate({\n                        config: newConfig,\n                        isConfigured: isNodeConfigured(node.type, newConfig)\n                    });\n                }\n            }\n        };\n        const handleNewRoleChange = (field, value)=>{\n            const newConfig = {\n                ...roleConfig,\n                [field]: value\n            };\n            setConfig(newConfig);\n            onUpdate({\n                config: newConfig,\n                isConfigured: isNodeConfigured(node.type, newConfig)\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Select Role\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 11\n                        }, this),\n                        isLoadingRoles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400\",\n                            children: \"Loading roles...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' ? 'create_new' : (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) || '',\n                            onChange: (e)=>handleRoleSelectionChange(e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a role...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"System Roles\",\n                                    children: _config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.id,\n                                            children: role.name\n                                        }, role.id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, this),\n                                customRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Your Custom Roles\",\n                                    children: customRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.role_id,\n                                            children: role.name\n                                        }, role.role_id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Create New\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"create_new\",\n                                        children: \"+ Create New Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 928,\n                            columnNumber: 13\n                        }, this),\n                        rolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error loading roles: \",\n                                rolesError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 9\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) !== 'new' && (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-white mb-1\",\n                            children: roleConfig.roleName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 13\n                        }, this),\n                        roleConfig.customPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300\",\n                            children: roleConfig.customPrompt\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 971,\n                    columnNumber: 11\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"New Role Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleName || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleName', e.target.value),\n                                    placeholder: \"e.g., Data Analyst, Creative Writer\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Role Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleDescription || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleDescription', e.target.value),\n                                    placeholder: \"Brief description of this role's purpose\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Custom Instructions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1013,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: roleConfig.customPrompt || '',\n                                    onChange: (e)=>handleNewRoleChange('customPrompt', e.target.value),\n                                    placeholder: \"Enter detailed instructions for this role...\",\n                                    rows: 4,\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1012,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.memoryEnabled) || false,\n                                    onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                    className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-300\",\n                                    children: \"Enable memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1029,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1 ml-6\",\n                            children: \"Allow this role to remember context from previous interactions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1038,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1028,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 917,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1050,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1058,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1053,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1049,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1068,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1067,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1082,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1081,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1094,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1097,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1093,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1080,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1048,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1112,\n            columnNumber: 7\n        }, this);\n    };\n    const renderCentralRouterConfig = ()=>{\n        const routerConfig = config;\n        var _routerConfig_enableCaching, _routerConfig_debugMode;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Routing Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.routingStrategy) || 'smart',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    routingStrategy: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"smart\",\n                                    children: \"Smart Routing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"round_robin\",\n                                    children: \"Round Robin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"load_balanced\",\n                                    children: \"Load Balanced\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"priority\",\n                                    children: \"Priority Based\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"How the router selects between available AI providers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Retries\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"0\",\n                            max: \"10\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.maxRetries) || 3,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    maxRetries: parseInt(e.target.value) || 3\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Number of retry attempts on failure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (ms)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1000\",\n                            max: \"300000\",\n                            step: \"1000\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.timeout) || 30000,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    timeout: parseInt(e.target.value) || 30000\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Request timeout in milliseconds\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1201,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Enable Caching\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_enableCaching = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.enableCaching) !== null && _routerConfig_enableCaching !== void 0 ? _routerConfig_enableCaching : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            enableCaching: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Cache responses to improve performance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Debug Mode\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_debugMode = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.debugMode) !== null && _routerConfig_debugMode !== void 0 ? _routerConfig_debugMode : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            debugMode: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Enable detailed logging for debugging\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1256,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1144,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolConfig = ()=>{\n        var _toolOptions_find, _toolOptions_find1;\n        const toolConfig = config;\n        const toolOptions = [\n            {\n                value: '',\n                label: 'Select a tool...'\n            },\n            {\n                value: 'google_drive',\n                label: '📁 Google Drive',\n                description: 'Access and manage Google Drive files'\n            },\n            {\n                value: 'google_docs',\n                label: '📄 Google Docs',\n                description: 'Create and edit Google Documents'\n            },\n            {\n                value: 'google_sheets',\n                label: '📊 Google Sheets',\n                description: 'Work with Google Spreadsheets'\n            },\n            {\n                value: 'zapier',\n                label: '⚡ Zapier',\n                description: 'Connect with 5000+ apps via Zapier'\n            },\n            {\n                value: 'notion',\n                label: '📝 Notion',\n                description: 'Access Notion databases and pages'\n            },\n            {\n                value: 'calendar',\n                label: '📅 Calendar',\n                description: 'Manage calendar events and schedules'\n            },\n            {\n                value: 'gmail',\n                label: '📧 Gmail',\n                description: 'Send and manage emails'\n            },\n            {\n                value: 'youtube',\n                label: '📺 YouTube',\n                description: 'Access YouTube data and analytics'\n            },\n            {\n                value: 'supabase',\n                label: '🗄️ Supabase',\n                description: 'Direct database operations'\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Tool Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    toolType: e.target.value,\n                                    // Reset tool-specific config when changing tool type\n                                    toolConfig: {},\n                                    // All tools need authentication\n                                    connectionStatus: 'disconnected',\n                                    isAuthenticated: false\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: toolOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: option.value,\n                                    children: option.label\n                                }, option.value, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1329,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1308,\n                            columnNumber: 11\n                        }, this),\n                        (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: (_toolOptions_find = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find === void 0 ? void 0 : _toolOptions_find.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1304,\n                    columnNumber: 9\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-yellow-400\",\n                                    children: \"Authentication Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mb-2\",\n                                    children: [\n                                        (_toolOptions_find1 = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find1 === void 0 ? void 0 : _toolOptions_find1.label,\n                                        \" integration coming soon!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"This tool will require account linking and authentication.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1353,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1349,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1343,\n                    columnNumber: 11\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1363,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"5\",\n                            max: \"300\",\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum time to wait for the tool operation to complete\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1384,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1362,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1303,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPlannerConfig = ()=>{\n        var _plannerConfig_parameters, _plannerConfig_parameters1, _plannerConfig_parameters2, _plannerConfig_parameters3;\n        _s1();\n        const plannerConfig = config;\n        // Get available models for the selected provider\n        const availableModels = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n            \"NodeConfigPanel.renderPlannerConfig.useMemo[availableModels]\": ()=>{\n                if (!(plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || !fetchedProviderModels) return [];\n                return fetchedProviderModels.filter({\n                    \"NodeConfigPanel.renderPlannerConfig.useMemo[availableModels]\": (model)=>model.provider_id === plannerConfig.providerId\n                }[\"NodeConfigPanel.renderPlannerConfig.useMemo[availableModels]\"]).map({\n                    \"NodeConfigPanel.renderPlannerConfig.useMemo[availableModels]\": (model)=>({\n                            value: model.id,\n                            label: model.display_name || model.name,\n                            model: model\n                        })\n                }[\"NodeConfigPanel.renderPlannerConfig.useMemo[availableModels]\"]);\n            }\n        }[\"NodeConfigPanel.renderPlannerConfig.useMemo[availableModels]\"], [\n            plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId,\n            fetchedProviderModels\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 0.7,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openai\",\n                                    children: \"OpenAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"anthropic\",\n                                    children: \"Anthropic\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"google\",\n                                    children: \"Google\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1442,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"deepseek\",\n                                    children: \"DeepSeek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1443,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"xai\",\n                                    children: \"xAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1444,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openrouter\",\n                                    children: \"OpenRouter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1445,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1415,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1411,\n                    columnNumber: 9\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1451,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                let updatedConfig = {\n                                    ...plannerConfig,\n                                    modelId: selectedModelId\n                                };\n                                // Set reasonable default for maxTokens based on model limits\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Model\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1489,\n                                    columnNumber: 15\n                                }, this),\n                                availableModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: model.value,\n                                        children: model.label\n                                    }, model.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1491,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1454,\n                            columnNumber: 13\n                        }, this),\n                        isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Loading models...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1497,\n                            columnNumber: 15\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-400 mt-1\",\n                            children: fetchProviderModelsError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1500,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1450,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1507,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    apiKey: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter API key or leave empty to use config keys\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1510,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Leave empty to use keys from My Models configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1527,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1506,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Max Tokens: \",\n                                        (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters = plannerConfig.parameters) === null || _plannerConfig_parameters === void 0 ? void 0 : _plannerConfig_parameters.maxTokens) || 'Auto'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1537,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: tokenLimits.minTokens,\n                                    max: tokenLimits.maxTokens,\n                                    value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters1 = plannerConfig.parameters) === null || _plannerConfig_parameters1 === void 0 ? void 0 : _plannerConfig_parameters1.maxTokens) || tokenLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...plannerConfig,\n                                            parameters: {\n                                                ...plannerConfig.parameters,\n                                                maxTokens: parseInt(e.target.value)\n                                            }\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tokenLimits.minTokens\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1562,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tokenLimits.maxTokens\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1563,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1561,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1536,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Temperature: \",\n                                        (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters2 = plannerConfig.parameters) === null || _plannerConfig_parameters2 === void 0 ? void 0 : _plannerConfig_parameters2.temperature) || 0.7\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1569,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters3 = plannerConfig.parameters) === null || _plannerConfig_parameters3 === void 0 ? void 0 : _plannerConfig_parameters3.temperature) || 0.7,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...plannerConfig,\n                                            parameters: {\n                                                ...plannerConfig.parameters,\n                                                temperature: parseFloat(e.target.value)\n                                            }\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1572,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"0 (Focused)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1595,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"2 (Creative)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1596,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1594,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1568,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Subtasks\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1605,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"50\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.maxSubtasks) || 10,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    maxSubtasks: parseInt(e.target.value) || 10\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1608,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum number of subtasks the planner can create\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1626,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1604,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1410,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(renderPlannerConfig, \"90Btq9JSpbfFDYWrwYOW62xCzhM=\");\n    const renderBrowsingConfig = ()=>{\n        var _browsingConfig_searchEngines, _browsingConfig_searchEngines1;\n        const browsingConfig = config;\n        var _browsingConfig_enableScreenshots, _browsingConfig_enableFormFilling, _browsingConfig_enableCaptchaSolving, _browsingConfig_searchEngines_includes, _browsingConfig_searchEngines_includes1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-green-900/20 border border-green-700 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1641,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-green-400\",\n                                    children: \"Intelligent Browsing Agent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1642,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1640,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-300\",\n                            children: \"This node automatically plans and executes complex web browsing tasks using AI.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1644,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1639,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Sites to Visit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1650,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"20\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.maxSites) || 5,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    maxSites: parseInt(e.target.value) || 5\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1653,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1649,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout per Operation (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1674,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"10\",\n                            max: \"300\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1677,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1673,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300\",\n                            children: \"Capabilities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1698,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableScreenshots = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableScreenshots) !== null && _browsingConfig_enableScreenshots !== void 0 ? _browsingConfig_enableScreenshots : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableScreenshots: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1703,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCF8 Take Screenshots\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1719,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1702,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableFormFilling = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableFormFilling) !== null && _browsingConfig_enableFormFilling !== void 0 ? _browsingConfig_enableFormFilling : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableFormFilling: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1723,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCDD Fill Forms Automatically\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1739,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1722,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableCaptchaSolving = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableCaptchaSolving) !== null && _browsingConfig_enableCaptchaSolving !== void 0 ? _browsingConfig_enableCaptchaSolving : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableCaptchaSolving: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1743,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDD10 Attempt CAPTCHA Solving\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1759,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1742,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1697,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Search Engines\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1764,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines === void 0 ? void 0 : _browsingConfig_searchEngines.includes('google')) !== null && _browsingConfig_searchEngines_includes !== void 0 ? _browsingConfig_searchEngines_includes : true,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'google'),\n                                                    'google'\n                                                ] : currentEngines.filter((eng)=>eng !== 'google');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1769,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1790,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1768,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes1 = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines1 = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines1 === void 0 ? void 0 : _browsingConfig_searchEngines1.includes('bing')) !== null && _browsingConfig_searchEngines_includes1 !== void 0 ? _browsingConfig_searchEngines_includes1 : false,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'bing'),\n                                                    'bing'\n                                                ] : currentEngines.filter((eng)=>eng !== 'bing');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1793,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Bing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1814,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1792,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1767,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1763,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1638,\n            columnNumber: 7\n        }, this);\n    };\n    const renderMemoryConfig = ()=>{\n        const memoryConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Memory Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1828,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...memoryConfig,\n                                    memoryType: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Memory Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1846,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"store\",\n                                    children: \"Store Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1847,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"retrieve\",\n                                    children: \"Retrieve Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1848,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"session\",\n                                    children: \"Session Memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1849,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"persistent\",\n                                    children: \"Persistent Memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1850,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1831,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Choose how this memory node will handle data\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1852,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1827,\n                    columnNumber: 9\n                }, this),\n                (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Storage Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1859,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.storageKey) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...memoryConfig,\n                                    storageKey: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter unique storage key (e.g., user_preferences, task_history)\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1862,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Unique identifier for this memory storage\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1879,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1858,\n                    columnNumber: 11\n                }, this),\n                (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.storageKey) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Storage Scope\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1888,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.storageScope) || 'workflow',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            storageScope: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"workflow\",\n                                            children: \"Workflow Scope\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1906,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"user\",\n                                            children: \"User Scope\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1907,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"global\",\n                                            children: \"Global Scope\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1908,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1891,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Determines who can access this memory data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1910,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1887,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Data Format\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1916,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.dataFormat) || 'text',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            dataFormat: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"text\",\n                                            children: \"Plain Text\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1934,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"json\",\n                                            children: \"JSON Object\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1935,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"structured\",\n                                            children: \"Structured Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1936,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1919,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Format for storing and retrieving data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1938,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1915,\n                            columnNumber: 13\n                        }, this),\n                        ((memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) === 'session' || (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) === 'persistent') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Max Storage Size (KB)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1945,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"1\",\n                                    max: \"10240\",\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.maxSize) || 1024,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            maxSize: parseInt(e.target.value) || 1024\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1948,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Maximum storage size limit in kilobytes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1966,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1944,\n                            columnNumber: 15\n                        }, this),\n                        (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) === 'session' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Time to Live (seconds)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1974,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"60\",\n                                    max: \"86400\",\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.ttl) || 3600,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            ttl: parseInt(e.target.value) || 3600\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1977,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"How long to keep session data (1 hour = 3600 seconds)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1995,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1973,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.encryption) || false,\n                                            onChange: (e)=>{\n                                                const newConfig = {\n                                                    ...memoryConfig,\n                                                    encryption: e.target.checked\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 2003,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-gray-300\",\n                                            children: \"Enable encryption\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 2019,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2002,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1 ml-6\",\n                                    children: \"Encrypt stored data for additional security\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2021,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 2001,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Description (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2027,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.description) || '',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            description: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    placeholder: \"Describe what this memory stores...\",\n                                    rows: 2,\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2030,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Optional description of what this memory node stores\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2047,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 2026,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1826,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'vision':\n                return renderVisionConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'centralRouter':\n                return renderCentralRouterConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            case 'tool':\n                return renderToolConfig();\n            case 'planner':\n                return renderPlannerConfig();\n            case 'browsing':\n                return renderBrowsingConfig();\n            case 'memory':\n                return renderMemoryConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2088,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2087,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 2091,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 2094,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2090,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2086,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 2103,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2099,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2085,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 2083,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"3qPqzANwA5DE2PGqVNbp9Fhx4wo=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});