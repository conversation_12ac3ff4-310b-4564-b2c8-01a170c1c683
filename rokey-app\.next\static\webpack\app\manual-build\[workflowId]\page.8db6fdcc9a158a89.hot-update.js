"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, hasRoleInput = false, hasToolsInput = false, hasBrowsingInput = false, inputLabel = 'Input', outputLabel = 'Output', roleInputLabel = 'Role', toolsInputLabel = 'Tools', browsingInputLabel = 'Browse', inputHandles = [], className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"input\",\n                        className: \"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                        style: {\n                            left: -12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-gray-300 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '45%'\n                        },\n                        children: inputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"role\",\n                        className: \"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                        style: {\n                            left: -12,\n                            top: '30%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-purple-200 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '25%'\n                        },\n                        children: roleInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            hasToolsInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"tools\",\n                        className: \"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors\",\n                        style: {\n                            left: -12,\n                            top: '70%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-green-200 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '65%'\n                        },\n                        children: toolsInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            hasBrowsingInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"browsing\",\n                        className: \"w-6 h-6 border-2 border-cyan-500 bg-cyan-700 hover:border-cyan-400 hover:bg-cyan-400 transition-colors\",\n                        style: {\n                            left: -12,\n                            top: '85%'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-cyan-200 font-medium pointer-events-none\",\n                        style: {\n                            left: -50,\n                            top: '80%'\n                        },\n                        children: browsingInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            inputHandles && inputHandles.length > 0 && inputHandles.map((handle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                            type: \"target\",\n                            position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                            id: handle.id,\n                            className: \"w-8 h-8 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors cursor-pointer rounded-full\",\n                            style: {\n                                left: -16,\n                                top: index === 0 ? '30%' : '60%',\n                                zIndex: 10\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-xs text-purple-200 font-medium pointer-events-none whitespace-nowrap\",\n                            style: {\n                                left: -70,\n                                top: index === 0 ? '25%' : '55%',\n                                zIndex: 5\n                            },\n                            children: handle.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, handle.id, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"source\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                        className: \"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors\",\n                        style: {\n                            right: -12\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute text-xs text-orange-200 font-medium pointer-events-none\",\n                        style: {\n                            right: -60,\n                            top: '45%'\n                        },\n                        children: outputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});