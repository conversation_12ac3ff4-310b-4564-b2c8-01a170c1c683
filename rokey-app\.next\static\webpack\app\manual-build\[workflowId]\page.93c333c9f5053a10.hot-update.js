"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Role management state\n    const [customRoles, setCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingRoles, setIsLoadingRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rolesError, setRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Fetch custom roles from database\n    const fetchCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchCustomRoles]\": async ()=>{\n            setIsLoadingRoles(true);\n            setRolesError(null);\n            try {\n                const response = await fetch('/api/user/custom-roles');\n                if (!response.ok) {\n                    throw new Error('Failed to fetch custom roles');\n                }\n                const roles = await response.json();\n                setCustomRoles(roles);\n            } catch (err) {\n                console.error('Error fetching custom roles:', err);\n                setRolesError(err.message);\n                setCustomRoles([]);\n            } finally{\n                setIsLoadingRoles(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchCustomRoles]\"], []);\n    // Load models and roles on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' || node.type === 'vision' || node.type === 'planner') {\n                fetchModelsFromDatabase();\n            }\n            if (node.type === 'roleAgent') {\n                fetchCustomRoles();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase,\n        fetchCustomRoles\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if ((node.type === 'provider' || node.type === 'vision' || node.type === 'planner') && fetchedProviderModels && fetchedProviderModels.length > 0) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {\n                    let availableModels1 = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels1 = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels1.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels1.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels1 = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels1.length > 0) {\n                        const selectedModelId = availableModels1[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                        const currentParams = providerConfig.parameters || {};\n                        // Update config in a single call to avoid infinite loops\n                        const newConfig = {\n                            ...providerConfig,\n                            modelId: selectedModelId,\n                            parameters: {\n                                ...currentParams,\n                                maxTokens: currentParams.maxTokens || reasonableDefault\n                            }\n                        };\n                        setConfig(newConfig);\n                        onUpdate({\n                            config: newConfig,\n                            isConfigured: isNodeConfigured(node.type, newConfig)\n                        });\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        node.type,\n        config === null || config === void 0 ? void 0 : config.providerId\n    ]); // Only re-run when provider changes\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value\n        };\n        // Only initialize parameters if they don't exist and we're setting a parameter\n        if (key === 'parameters' || !currentConfig.parameters) {\n            newConfig.parameters = {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined,\n                ...currentConfig.parameters,\n                ...key === 'parameters' ? value : {}\n            };\n        }\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision' || node.type === 'planner')) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // Filter function for vision nodes - only show multimodal models\n                const filterForVision = {\n                    \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (models)=>{\n                        if (node.type === 'vision') {\n                            return models.filter({\n                                \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (model)=>model.modality && (model.modality.includes('multimodal') || model.modality.includes('vision') || model.modality.includes('image'))\n                            }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"]);\n                        }\n                        return models;\n                    }\n                }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"];\n                // If the selected provider is \"OpenRouter\", show all fetched models (filtered for vision if needed)\n                if (currentProviderDetails.id === \"openrouter\") {\n                    const filteredModels = filterForVision(fetchedProviderModels);\n                    return filteredModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    var _deepseekChatModel_modality, _deepseekReasonerModel_modality;\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel && (node.type === 'provider' || node.type === 'planner' || node.type === 'vision' && ((_deepseekChatModel_modality = deepseekChatModel.modality) === null || _deepseekChatModel_modality === void 0 ? void 0 : _deepseekChatModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel && (node.type === 'provider' || node.type === 'planner' || node.type === 'vision' && ((_deepseekReasonerModel_modality = deepseekReasonerModel.modality) === null || _deepseekReasonerModel_modality === void 0 ? void 0 : _deepseekReasonerModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id and vision capabilities\n                const providerModels = fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions].providerModels\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions].providerModels\"]);\n                const filteredModels = filterForVision(providerModels);\n                return filteredModels.map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider' && node.type !== 'vision' && node.type !== 'planner') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            return {\n                maxTokens,\n                minTokens\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'vision':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'roleAgent':\n                if (nodeConfig.roleType === 'new') {\n                    return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);\n                }\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'centralRouter':\n                return !!nodeConfig.routingStrategy;\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'planner':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'browsing':\n                return true; // Browsing node is always configured with defaults\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1, _providerConfig_parameters2, _providerConfig_parameters3;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key for this provider\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Required: Enter your own API key for this AI provider (BYOK)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...providerConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters2 = providerConfig.parameters) === null || _providerConfig_parameters2 === void 0 ? void 0 : _providerConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters3 = providerConfig.parameters) === null || _providerConfig_parameters3 === void 0 ? void 0 : _providerConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, this);\n    };\n    const renderVisionConfig = ()=>{\n        var _visionConfig_parameters, _visionConfig_parameters1, _visionConfig_parameters2, _visionConfig_parameters3;\n        const visionConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key for this provider\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Required: Enter your own API key for this AI provider (BYOK)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Vision Model\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-purple-400 ml-1\",\n                                    children: \"(Multimodal Only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...visionConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Vision Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No vision models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 11\n                        }, this),\n                        modelOptions.length === 0 && (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) && !isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg\",\n                            children: \"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Temperature (0.0 - 2.0)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters = visionConfig.parameters) === null || _visionConfig_parameters === void 0 ? void 0 : _visionConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters1 = visionConfig.parameters) === null || _visionConfig_parameters1 === void 0 ? void 0 : _visionConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters2 = visionConfig.parameters) === null || _visionConfig_parameters2 === void 0 ? void 0 : _visionConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters3 = visionConfig.parameters) === null || _visionConfig_parameters3 === void 0 ? void 0 : _visionConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate for vision analysis.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 785,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 778,\n                    columnNumber: 9\n                }, this),\n                (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 font-medium mb-1\",\n                            children: \"\\uD83D\\uDC41️ Vision Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-purple-200\",\n                            children: \"Access to multimodal models from multiple providers for image analysis and vision tasks.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 597,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        const roleConfig = config;\n        // Combine predefined and custom roles for dropdown\n        const availableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>({\n                    id: role.id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'predefined'\n                })),\n            ...customRoles.map((role)=>({\n                    id: role.role_id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'custom'\n                }))\n        ];\n        const handleRoleSelectionChange = (value)=>{\n            if (value === 'create_new') {\n                // Switch to create new role mode\n                const newConfig = {\n                    ...roleConfig,\n                    roleType: 'new',\n                    roleId: '',\n                    roleName: '',\n                    newRoleName: '',\n                    newRoleDescription: '',\n                    customPrompt: ''\n                };\n                setConfig(newConfig);\n                onUpdate({\n                    config: newConfig,\n                    isConfigured: isNodeConfigured(node.type, newConfig)\n                });\n            } else {\n                // Select existing role\n                const selectedRole = availableRoles.find((role)=>role.id === value);\n                if (selectedRole) {\n                    const newConfig = {\n                        ...roleConfig,\n                        roleType: selectedRole.type,\n                        roleId: selectedRole.id,\n                        roleName: selectedRole.name,\n                        customPrompt: selectedRole.description || ''\n                    };\n                    setConfig(newConfig);\n                    onUpdate({\n                        config: newConfig,\n                        isConfigured: isNodeConfigured(node.type, newConfig)\n                    });\n                }\n            }\n        };\n        const handleNewRoleChange = (field, value)=>{\n            const newConfig = {\n                ...roleConfig,\n                [field]: value\n            };\n            setConfig(newConfig);\n            onUpdate({\n                config: newConfig,\n                isConfigured: isNodeConfigured(node.type, newConfig)\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Select Role\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 928,\n                            columnNumber: 11\n                        }, this),\n                        isLoadingRoles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400\",\n                            children: \"Loading roles...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' ? 'create_new' : (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) || '',\n                            onChange: (e)=>handleRoleSelectionChange(e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a role...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"System Roles\",\n                                    children: _config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.id,\n                                            children: role.name\n                                        }, role.id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 946,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                customRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Your Custom Roles\",\n                                    children: customRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.role_id,\n                                            children: role.name\n                                        }, role.role_id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Create New\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"create_new\",\n                                        children: \"+ Create New Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 13\n                        }, this),\n                        rolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error loading roles: \",\n                                rolesError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 927,\n                    columnNumber: 9\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) !== 'new' && (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-white mb-1\",\n                            children: roleConfig.roleName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 13\n                        }, this),\n                        roleConfig.customPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300\",\n                            children: roleConfig.customPrompt\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 984,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 979,\n                    columnNumber: 11\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"New Role Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleName || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleName', e.target.value),\n                                    placeholder: \"e.g., Data Analyst, Creative Writer\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 994,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Role Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleDescription || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleDescription', e.target.value),\n                                    placeholder: \"Brief description of this role's purpose\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Custom Instructions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: roleConfig.customPrompt || '',\n                                    onChange: (e)=>handleNewRoleChange('customPrompt', e.target.value),\n                                    placeholder: \"Enter detailed instructions for this role...\",\n                                    rows: 4,\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1024,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.memoryEnabled) || false,\n                                    onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                    className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1038,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-300\",\n                                    children: \"Enable memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1 ml-6\",\n                            children: \"Allow this role to remember context from previous interactions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1036,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 925,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1058,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1066,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1069,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1057,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1076,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1079,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1075,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1088,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1056,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1133,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1120,\n            columnNumber: 7\n        }, this);\n    };\n    const renderCentralRouterConfig = ()=>{\n        const routerConfig = config;\n        var _routerConfig_enableCaching, _routerConfig_debugMode;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Routing Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.routingStrategy) || 'smart',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    routingStrategy: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"smart\",\n                                    children: \"Smart Routing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"round_robin\",\n                                    children: \"Round Robin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"load_balanced\",\n                                    children: \"Load Balanced\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"priority\",\n                                    children: \"Priority Based\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"How the router selects between available AI providers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Retries\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"0\",\n                            max: \"10\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.maxRetries) || 3,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    maxRetries: parseInt(e.target.value) || 3\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Number of retry attempts on failure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (ms)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1000\",\n                            max: \"300000\",\n                            step: \"1000\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.timeout) || 30000,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    timeout: parseInt(e.target.value) || 30000\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Request timeout in milliseconds\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Enable Caching\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_enableCaching = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.enableCaching) !== null && _routerConfig_enableCaching !== void 0 ? _routerConfig_enableCaching : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            enableCaching: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Cache responses to improve performance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1237,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Debug Mode\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_debugMode = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.debugMode) !== null && _routerConfig_debugMode !== void 0 ? _routerConfig_debugMode : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            debugMode: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Enable detailed logging for debugging\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1264,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1152,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolConfig = ()=>{\n        var _toolOptions_find, _toolOptions_find1;\n        const toolConfig = config;\n        const toolOptions = [\n            {\n                value: '',\n                label: 'Select a tool...'\n            },\n            {\n                value: 'google_drive',\n                label: '📁 Google Drive',\n                description: 'Access and manage Google Drive files'\n            },\n            {\n                value: 'google_docs',\n                label: '📄 Google Docs',\n                description: 'Create and edit Google Documents'\n            },\n            {\n                value: 'google_sheets',\n                label: '📊 Google Sheets',\n                description: 'Work with Google Spreadsheets'\n            },\n            {\n                value: 'zapier',\n                label: '⚡ Zapier',\n                description: 'Connect with 5000+ apps via Zapier'\n            },\n            {\n                value: 'notion',\n                label: '📝 Notion',\n                description: 'Access Notion databases and pages'\n            },\n            {\n                value: 'calendar',\n                label: '📅 Calendar',\n                description: 'Manage calendar events and schedules'\n            },\n            {\n                value: 'gmail',\n                label: '📧 Gmail',\n                description: 'Send and manage emails'\n            },\n            {\n                value: 'youtube',\n                label: '📺 YouTube',\n                description: 'Access YouTube data and analytics'\n            },\n            {\n                value: 'supabase',\n                label: '🗄️ Supabase',\n                description: 'Direct database operations'\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Tool Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    toolType: e.target.value,\n                                    // Reset tool-specific config when changing tool type\n                                    toolConfig: {},\n                                    // All tools need authentication\n                                    connectionStatus: 'disconnected',\n                                    isAuthenticated: false\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: toolOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: option.value,\n                                    children: option.label\n                                }, option.value, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1337,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1316,\n                            columnNumber: 11\n                        }, this),\n                        (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: (_toolOptions_find = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find === void 0 ? void 0 : _toolOptions_find.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1312,\n                    columnNumber: 9\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-yellow-400\",\n                                    children: \"Authentication Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1354,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1352,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mb-2\",\n                                    children: [\n                                        (_toolOptions_find1 = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find1 === void 0 ? void 0 : _toolOptions_find1.label,\n                                        \" integration coming soon!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"This tool will require account linking and authentication.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1361,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1351,\n                    columnNumber: 11\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1371,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"5\",\n                            max: \"300\",\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1374,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum time to wait for the tool operation to complete\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1392,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1370,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1311,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPlannerConfig = ()=>{\n        var _plannerConfig_parameters, _plannerConfig_parameters1, _plannerConfig_parameters2, _plannerConfig_parameters3;\n        const plannerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 0.7,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openai\",\n                                    children: \"OpenAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"anthropic\",\n                                    children: \"Anthropic\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"google\",\n                                    children: \"Google\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"deepseek\",\n                                    children: \"DeepSeek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"xai\",\n                                    children: \"xAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openrouter\",\n                                    children: \"OpenRouter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1440,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1410,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1406,\n                    columnNumber: 9\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1446,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                let updatedConfig = {\n                                    ...plannerConfig,\n                                    modelId: selectedModelId\n                                };\n                                // Set reasonable default for maxTokens based on model limits\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Model\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1484,\n                                    columnNumber: 15\n                                }, this),\n                                availableModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: model.value,\n                                        children: model.label\n                                    }, model.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1486,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1449,\n                            columnNumber: 13\n                        }, this),\n                        isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Loading models...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1492,\n                            columnNumber: 15\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-400 mt-1\",\n                            children: fetchProviderModelsError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1495,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1445,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1502,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    apiKey: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter your API key for this provider\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1505,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Required: Enter your own API key for this AI provider (BYOK)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1523,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1501,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Max Tokens: \",\n                                        (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters = plannerConfig.parameters) === null || _plannerConfig_parameters === void 0 ? void 0 : _plannerConfig_parameters.maxTokens) || 'Auto'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1533,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters1 = plannerConfig.parameters) === null || _plannerConfig_parameters1 === void 0 ? void 0 : _plannerConfig_parameters1.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...plannerConfig,\n                                            parameters: {\n                                                ...plannerConfig.parameters,\n                                                maxTokens: parseInt(e.target.value)\n                                            }\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getCurrentModelLimits.minTokens\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getCurrentModelLimits.maxTokens\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1559,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1557,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1532,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: [\n                                        \"Temperature: \",\n                                        (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters2 = plannerConfig.parameters) === null || _plannerConfig_parameters2 === void 0 ? void 0 : _plannerConfig_parameters2.temperature) || 0.7\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1565,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters3 = plannerConfig.parameters) === null || _plannerConfig_parameters3 === void 0 ? void 0 : _plannerConfig_parameters3.temperature) || 0.7,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...plannerConfig,\n                                            parameters: {\n                                                ...plannerConfig.parameters,\n                                                temperature: parseFloat(e.target.value)\n                                            }\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1568,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"0 (Focused)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"2 (Creative)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1592,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1590,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1564,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Subtasks\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1601,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"50\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.maxSubtasks) || 10,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    maxSubtasks: parseInt(e.target.value) || 10\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum number of subtasks the planner can create\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1622,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1600,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1405,\n            columnNumber: 7\n        }, this);\n    };\n    const renderBrowsingConfig = ()=>{\n        var _browsingConfig_searchEngines, _browsingConfig_searchEngines1;\n        const browsingConfig = config;\n        var _browsingConfig_enableScreenshots, _browsingConfig_enableFormFilling, _browsingConfig_enableCaptchaSolving, _browsingConfig_searchEngines_includes, _browsingConfig_searchEngines_includes1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-green-900/20 border border-green-700 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1637,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-green-400\",\n                                    children: \"Intelligent Browsing Agent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1638,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-300\",\n                            children: \"This node automatically plans and executes complex web browsing tasks using AI.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1640,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1635,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Sites to Visit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"20\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.maxSites) || 5,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    maxSites: parseInt(e.target.value) || 5\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1645,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout per Operation (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1670,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"10\",\n                            max: \"300\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1673,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1669,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300\",\n                            children: \"Capabilities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1694,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableScreenshots = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableScreenshots) !== null && _browsingConfig_enableScreenshots !== void 0 ? _browsingConfig_enableScreenshots : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableScreenshots: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1699,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCF8 Take Screenshots\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1715,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1698,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableFormFilling = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableFormFilling) !== null && _browsingConfig_enableFormFilling !== void 0 ? _browsingConfig_enableFormFilling : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableFormFilling: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1719,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCDD Fill Forms Automatically\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1735,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1718,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableCaptchaSolving = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableCaptchaSolving) !== null && _browsingConfig_enableCaptchaSolving !== void 0 ? _browsingConfig_enableCaptchaSolving : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableCaptchaSolving: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1739,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDD10 Attempt CAPTCHA Solving\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1755,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1738,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1693,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Search Engines\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1760,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines === void 0 ? void 0 : _browsingConfig_searchEngines.includes('google')) !== null && _browsingConfig_searchEngines_includes !== void 0 ? _browsingConfig_searchEngines_includes : true,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'google'),\n                                                    'google'\n                                                ] : currentEngines.filter((eng)=>eng !== 'google');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1765,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1786,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1764,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes1 = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines1 = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines1 === void 0 ? void 0 : _browsingConfig_searchEngines1.includes('bing')) !== null && _browsingConfig_searchEngines_includes1 !== void 0 ? _browsingConfig_searchEngines_includes1 : false,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'bing'),\n                                                    'bing'\n                                                ] : currentEngines.filter((eng)=>eng !== 'bing');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1789,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Bing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1810,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1788,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1763,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1759,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1634,\n            columnNumber: 7\n        }, this);\n    };\n    const renderMemoryConfig = ()=>{\n        const memoryConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Memory Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1824,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...memoryConfig,\n                                    memoryType: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Memory Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1842,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"store\",\n                                    children: \"Store Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1843,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"retrieve\",\n                                    children: \"Retrieve Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1844,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"session\",\n                                    children: \"Session Memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1845,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"persistent\",\n                                    children: \"Persistent Memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1846,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1827,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Choose how this memory node will handle data\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1848,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1823,\n                    columnNumber: 9\n                }, this),\n                (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Storage Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1855,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.storageKey) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...memoryConfig,\n                                    storageKey: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter unique storage key (e.g., user_preferences, task_history)\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1858,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Unique identifier for this memory storage\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1875,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1854,\n                    columnNumber: 11\n                }, this),\n                (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.storageKey) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Storage Scope\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1884,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.storageScope) || 'workflow',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            storageScope: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"workflow\",\n                                            children: \"Workflow Scope\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1902,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"user\",\n                                            children: \"User Scope\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1903,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"global\",\n                                            children: \"Global Scope\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1904,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1887,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Determines who can access this memory data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1906,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1883,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Data Format\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1912,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.dataFormat) || 'text',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            dataFormat: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"text\",\n                                            children: \"Plain Text\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1930,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"json\",\n                                            children: \"JSON Object\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1931,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"structured\",\n                                            children: \"Structured Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1932,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1915,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Format for storing and retrieving data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1934,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1911,\n                            columnNumber: 13\n                        }, this),\n                        ((memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) === 'session' || (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) === 'persistent') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Max Storage Size (KB)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1941,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"1\",\n                                    max: \"10240\",\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.maxSize) || 1024,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            maxSize: parseInt(e.target.value) || 1024\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1944,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Maximum storage size limit in kilobytes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1962,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1940,\n                            columnNumber: 15\n                        }, this),\n                        (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.memoryType) === 'session' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Time to Live (seconds)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1970,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"60\",\n                                    max: \"86400\",\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.ttl) || 3600,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            ttl: parseInt(e.target.value) || 3600\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1973,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"How long to keep session data (1 hour = 3600 seconds)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1991,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1969,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.encryption) || false,\n                                            onChange: (e)=>{\n                                                const newConfig = {\n                                                    ...memoryConfig,\n                                                    encryption: e.target.checked\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1999,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-gray-300\",\n                                            children: \"Enable encryption\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 2015,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1998,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1 ml-6\",\n                                    children: \"Encrypt stored data for additional security\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2017,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1997,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Description (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2023,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: (memoryConfig === null || memoryConfig === void 0 ? void 0 : memoryConfig.description) || '',\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...memoryConfig,\n                                            description: e.target.value\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    placeholder: \"Describe what this memory stores...\",\n                                    rows: 2,\n                                    className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2026,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: \"Optional description of what this memory node stores\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2043,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 2022,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1822,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'vision':\n                return renderVisionConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'centralRouter':\n                return renderCentralRouterConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            case 'tool':\n                return renderToolConfig();\n            case 'planner':\n                return renderPlannerConfig();\n            case 'browsing':\n                return renderBrowsingConfig();\n            case 'memory':\n                return renderMemoryConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 2084,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2083,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 2087,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 2090,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2086,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2082,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 2099,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2095,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2081,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 2114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 2118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 2109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 2079,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"3qPqzANwA5DE2PGqVNbp9Fhx4wo=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});