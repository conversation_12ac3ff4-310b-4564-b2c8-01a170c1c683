"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/manual-build/[workflowId]/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowEditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react/dist/style.css */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/style.css\");\n/* harmony import */ var _components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/manual-build/WorkflowToolbar */ \"(app-pages-browser)/./src/components/manual-build/WorkflowToolbar.tsx\");\n/* harmony import */ var _components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/manual-build/NodePalette */ \"(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\");\n/* harmony import */ var _components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/manual-build/NodeConfigPanel */ \"(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\");\n/* harmony import */ var _components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/manual-build/ContextMenu */ \"(app-pages-browser)/./src/components/manual-build/ContextMenu.tsx\");\n/* harmony import */ var _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/manual-build/nodes */ \"(app-pages-browser)/./src/components/manual-build/nodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WorkflowEditorPage(param) {\n    let { params } = param;\n    _s();\n    const resolvedParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const workflowId = resolvedParams === null || resolvedParams === void 0 ? void 0 : resolvedParams.workflowId;\n    const [workflow, setWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useNodesState)([]);\n    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useEdgesState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDirty, setIsDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load workflow data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkflowEditorPage.useEffect\": ()=>{\n            if (workflowId === 'new') {\n                initializeNewWorkflow();\n            } else {\n                loadWorkflow(workflowId);\n            }\n        }\n    }[\"WorkflowEditorPage.useEffect\"], [\n        workflowId\n    ]);\n    const initializeNewWorkflow = async ()=>{\n        try {\n            // Create default nodes for new workflow\n            const defaultNodes = [\n                {\n                    id: 'user-request',\n                    type: 'userRequest',\n                    position: {\n                        x: 50,\n                        y: 200\n                    },\n                    data: {\n                        label: 'User Request',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Starting point for user input'\n                    }\n                },\n                {\n                    id: 'classifier',\n                    type: 'classifier',\n                    position: {\n                        x: 350,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Classifier',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Analyzes and categorizes the request'\n                    }\n                },\n                {\n                    id: 'output',\n                    type: 'output',\n                    position: {\n                        x: 950,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Output',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Final response to the user'\n                    }\n                }\n            ];\n            const defaultEdges = [\n                {\n                    id: 'e1',\n                    source: 'user-request',\n                    target: 'classifier',\n                    type: 'smoothstep',\n                    animated: true\n                }\n            ];\n            setNodes(defaultNodes);\n            setEdges(defaultEdges);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to initialize new workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const loadWorkflow = async (id)=>{\n        try {\n            // TODO: Implement API call to load workflow\n            console.log('Loading workflow:', id);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to load workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onConnect]\": (params)=>{\n            const newEdge = {\n                ...params,\n                id: \"e\".concat(edges.length + 1),\n                type: 'smoothstep',\n                animated: true\n            };\n            setEdges({\n                \"WorkflowEditorPage.useCallback[onConnect]\": (eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_10__.addEdge)(newEdge, eds)\n            }[\"WorkflowEditorPage.useCallback[onConnect]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[onConnect]\"], [\n        edges.length,\n        setEdges\n    ]);\n    const onNodeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeClick]\": (event, node)=>{\n            setSelectedNode(node);\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeClick]\"], []);\n    const onPaneClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onPaneClick]\": ()=>{\n            setSelectedNode(null);\n            setContextMenu(null);\n        }\n    }[\"WorkflowEditorPage.useCallback[onPaneClick]\"], []);\n    const onNodeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeContextMenu]\": (event, node)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: node.id,\n                type: 'node',\n                nodeType: node.type,\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeContextMenu]\"], []);\n    const onEdgeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onEdgeContextMenu]\": (event, edge)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: edge.id,\n                type: 'edge',\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onEdgeContextMenu]\"], []);\n    const handleDeleteNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nodeId)=>{\n            // Don't delete core nodes\n            const coreNodes = [\n                'user-request',\n                'classifier',\n                'output'\n            ];\n            if (coreNodes.includes(nodeId)) return;\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nds)=>nds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (node)=>node.id !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (edge)=>edge.source !== nodeId && edge.target !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setIsDirty(true);\n            // Close config panel if deleted node was selected\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"], [\n        selectedNode,\n        setNodes,\n        setEdges\n    ]);\n    const handleDeleteEdge = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edgeId)=>{\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edge)=>edge.id !== edgeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"], [\n        setEdges\n    ]);\n    const handleDuplicateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nodeId)=>{\n            const nodeToDuplicate = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\"]);\n            if (!nodeToDuplicate) return;\n            const newNode = {\n                ...nodeToDuplicate,\n                id: \"\".concat(nodeToDuplicate.type, \"-\").concat(Date.now()),\n                position: {\n                    x: nodeToDuplicate.position.x + 50,\n                    y: nodeToDuplicate.position.y + 50\n                },\n                data: {\n                    ...nodeToDuplicate.data,\n                    label: \"\".concat(nodeToDuplicate.data.label, \" Copy\")\n                }\n            };\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nds)=>[\n                        ...nds,\n                        newNode\n                    ]\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"], [\n        nodes,\n        setNodes\n    ]);\n    const handleConfigureNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleConfigureNode]\": (nodeId)=>{\n            const node = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleConfigureNode].node\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleConfigureNode].node\"]);\n            if (node) {\n                setSelectedNode(node);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleConfigureNode]\"], [\n        nodes\n    ]);\n    const handleSave = async ()=>{\n        if (!workflow && workflowId === 'new') {\n            // Show save dialog for new workflow\n            const name = prompt('Enter workflow name:');\n            if (!name) return;\n            // TODO: Implement save new workflow\n            console.log('Saving new workflow:', name);\n        } else {\n            // Update existing workflow\n            setIsSaving(true);\n            try {\n                // TODO: Implement update workflow API call\n                console.log('Updating workflow:', workflowId);\n                setIsDirty(false);\n            } catch (error) {\n                console.error('Failed to save workflow:', error);\n            } finally{\n                setIsSaving(false);\n            }\n        }\n    };\n    const handleExecute = async ()=>{\n        if (!workflow) {\n            console.error('No workflow to execute');\n            return;\n        }\n        try {\n            console.log('🚀 Starting workflow execution...');\n            // Get user input for the workflow\n            const userInput = prompt('Enter your request for the workflow:');\n            if (!userInput) {\n                console.log('Workflow execution cancelled - no input provided');\n                return;\n            }\n            const response = await fetch('/api/manual-build/execute-workflow', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    workflowId: workflow.id,\n                    nodes,\n                    edges,\n                    userInput\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.details || 'Workflow execution failed');\n            }\n            const result = await response.json();\n            console.log('✅ Workflow execution completed:', result);\n            // Show result to user\n            alert(\"Workflow completed successfully!\\n\\nResult: \".concat(JSON.stringify(result.result, null, 2)));\n        } catch (error) {\n            console.error('❌ Workflow execution failed:', error);\n            alert(\"Workflow execution failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    };\n    const handleAddNode = (nodeType, position)=>{\n        let defaultConfig = {};\n        let isConfigured = true;\n        // Set proper default config for specific node types\n        if (nodeType === 'provider') {\n            defaultConfig = {\n                providerId: '',\n                modelId: '',\n                apiKey: '',\n                parameters: {\n                    temperature: 1.0,\n                    maxTokens: undefined,\n                    topP: undefined,\n                    frequencyPenalty: undefined,\n                    presencePenalty: undefined\n                }\n            };\n            isConfigured = false;\n        } else if (nodeType === 'centralRouter') {\n            defaultConfig = {\n                routingStrategy: 'smart',\n                fallbackProvider: '',\n                maxRetries: 3,\n                timeout: 30000,\n                enableCaching: true,\n                debugMode: false\n            };\n            isConfigured = true;\n        }\n        const newNode = {\n            id: \"\".concat(nodeType, \"-\").concat(Date.now()),\n            type: nodeType,\n            position,\n            data: {\n                label: nodeType === 'centralRouter' ? 'Central Router' : nodeType.charAt(0).toUpperCase() + nodeType.slice(1),\n                config: defaultConfig,\n                isConfigured,\n                description: \"\".concat(nodeType, \" node\")\n            }\n        };\n        setNodes((nds)=>[\n                ...nds,\n                newNode\n            ]);\n        setIsDirty(true);\n    };\n    const handleNodeUpdate = (nodeId, updates)=>{\n        setNodes((nds)=>nds.map((node)=>node.id === nodeId ? {\n                    ...node,\n                    data: {\n                        ...node.data,\n                        ...updates\n                    }\n                } : node));\n        setIsDirty(true);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading workflow...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#040716] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                workflow: workflow,\n                isDirty: isDirty,\n                isSaving: isSaving,\n                onSave: handleSave,\n                onExecute: handleExecute,\n                onBack: ()=>router.push('/manual-build')\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddNode: handleAddNode\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative manual-build-canvas\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.ReactFlow, {\n                                nodes: nodes,\n                                edges: edges,\n                                onNodesChange: onNodesChange,\n                                onEdgesChange: onEdgesChange,\n                                onConnect: onConnect,\n                                onNodeClick: onNodeClick,\n                                onNodeContextMenu: onNodeContextMenu,\n                                onEdgeContextMenu: onEdgeContextMenu,\n                                onPaneClick: onPaneClick,\n                                nodeTypes: _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__.nodeTypes,\n                                fitView: true,\n                                className: \"bg-[#040716]\",\n                                defaultViewport: {\n                                    x: 0,\n                                    y: 0,\n                                    zoom: 0.8\n                                },\n                                connectionLineStyle: {\n                                    stroke: '#ff6b35',\n                                    strokeWidth: 2\n                                },\n                                defaultEdgeOptions: {\n                                    style: {\n                                        stroke: '#ff6b35',\n                                        strokeWidth: 2\n                                    },\n                                    type: 'smoothstep',\n                                    animated: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.Background, {\n                                        color: \"#1f2937\",\n                                        gap: 20,\n                                        size: 1,\n                                        variant: \"dots\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.Controls, {\n                                        className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                        showInteractive: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.MiniMap, {\n                                        className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                        nodeColor: \"#ff6b35\",\n                                        maskColor: \"rgba(0, 0, 0, 0.2)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this),\n                            contextMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                id: contextMenu.id,\n                                type: contextMenu.type,\n                                nodeType: contextMenu.nodeType,\n                                top: contextMenu.y,\n                                left: contextMenu.x,\n                                onClose: ()=>setContextMenu(null),\n                                onDelete: contextMenu.type === 'node' ? handleDeleteNode : handleDeleteEdge,\n                                onDuplicate: contextMenu.type === 'node' ? handleDuplicateNode : undefined,\n                                onConfigure: contextMenu.type === 'node' ? handleConfigureNode : undefined,\n                                onDisconnect: contextMenu.type === 'edge' ? handleDeleteEdge : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        node: selectedNode,\n                        onUpdate: (updates)=>handleNodeUpdate(selectedNode.id, updates),\n                        onClose: ()=>setSelectedNode(null)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n        lineNumber: 345,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowEditorPage, \"GiKoqOcAXo1NotOy4cq/1dWBc14=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useNodesState,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useEdgesState\n    ];\n});\n_c = WorkflowEditorPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx\n"));

/***/ })

});