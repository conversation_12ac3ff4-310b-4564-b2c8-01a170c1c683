"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Role plugin for AI providers (connect to role input)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'centralRouter',\n                label: 'Central Router',\n                description: 'Smart routing hub for multiple AI providers and vision models',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Central Router',\n                    config: {\n                        routingStrategy: 'smart',\n                        fallbackProvider: '',\n                        maxRetries: 3,\n                        timeout: 30000,\n                        enableCaching: true,\n                        debugMode: false\n                    },\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'planner',\n                label: 'Planner',\n                description: 'AI model that creates browsing strategies and todo lists',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Planner',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 0.7,\n                            maxTokens: 1000\n                        },\n                        maxSubtasks: 10\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    logic: {\n        label: 'Logic & Control',\n        description: 'Flow control and decision making',\n        nodes: [\n            {\n                type: 'conditional',\n                label: 'Conditional',\n                description: 'Branch workflow based on conditions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Conditional',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'merge',\n                label: 'Merge',\n                description: 'Combine multiple inputs',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Merge',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'switch',\n                label: 'Switch',\n                description: 'Route to different paths',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Switch',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'loop',\n                label: 'Loop',\n                description: 'Repeat operations',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Loop',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tools',\n                description: 'External tool integrations (Google Drive, Zapier, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tools',\n                    config: {\n                        toolType: '',\n                        toolConfig: {},\n                        timeout: 30,\n                        connectionStatus: 'disconnected',\n                        isAuthenticated: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data across workflow executions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {\n                        memoryName: '',\n                        maxSize: 10240,\n                        encryption: true,\n                        description: ''\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    browsing: {\n        label: 'Web Browsing',\n        description: 'Intelligent web browsing and automation',\n        nodes: [\n            {\n                type: 'browsing',\n                label: 'Browsing Agent',\n                description: 'Intelligent web browsing agent with multi-step automation',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Browsing Agent',\n                    config: {\n                        maxSites: 5,\n                        timeout: 30,\n                        enableScreenshots: true,\n                        enableFormFilling: true,\n                        enableCaptchaSolving: false,\n                        searchEngines: [\n                            'google'\n                        ],\n                        maxDepth: 2,\n                        respectRobots: true,\n                        enableJavaScript: true\n                    },\n                    isConfigured: true\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 338,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 390,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});