"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/MemoryNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemoryNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleStackIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MemoryNode(param) {\n    let { data, id } = param;\n    _s();\n    const config = data.config;\n    const memoryName = config === null || config === void 0 ? void 0 : config.memoryName;\n    const maxSize = (config === null || config === void 0 ? void 0 : config.maxSize) || 10240; // 10MB default\n    const encryption = (config === null || config === void 0 ? void 0 : config.encryption) !== false; // Default true\n    const edges = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges)();\n    // Find connected nodes\n    const connectedNodes = edges.filter((edge)=>edge.source === id).map((edge)=>edge.target);\n    const [memoryStats, setMemoryStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MemoryNode.useEffect\": ()=>{\n            // Simulate memory stats - in real implementation, fetch from MemoryService\n            if (memoryName) {\n                setMemoryStats({\n                    entriesCount: Math.floor(Math.random() * 50) + 1,\n                    totalSize: \"\".concat((Math.random() * (maxSize / 1024)).toFixed(1), \"MB\"),\n                    lastUpdate: new Date().toLocaleTimeString()\n                });\n            }\n        }\n    }[\"MemoryNode.useEffect\"], [\n        memoryName,\n        maxSize\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CircleStackIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#ec4899\",\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: memoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: [\n                            \"\\uD83E\\uDDE0 \",\n                            memoryName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Max: \",\n                            Math.round(maxSize / 1024),\n                            \"MB | \",\n                            encryption ? '🔒 Encrypted' : '🔓 Plain'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this),\n                    memoryStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    memoryStats.entriesCount,\n                                    \" entries\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCBE \",\n                                    memoryStats.totalSize,\n                                    \" used\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDD52 Updated \",\n                                    memoryStats.lastUpdate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 15\n                    }, this),\n                    connectedNodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"\\uD83D\\uDD17 Connected to \",\n                            connectedNodes.length,\n                            \" node\",\n                            connectedNodes.length > 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded\",\n                        children: \"✅ Active & Ready\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 48,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"\\uD83E\\uDDE0 Plug & Play Memory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n                lineNumber: 76,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\MemoryNode.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(MemoryNode, \"OnhTheIx/6azcxZ0B54c5UycjQo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_3__.useEdges\n    ];\n});\n_c = MemoryNode;\nvar _c;\n$RefreshReg$(_c, \"MemoryNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\n"));

/***/ })

});