"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Role management state\n    const [customRoles, setCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingRoles, setIsLoadingRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rolesError, setRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Fetch custom roles from database\n    const fetchCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchCustomRoles]\": async ()=>{\n            setIsLoadingRoles(true);\n            setRolesError(null);\n            try {\n                const response = await fetch('/api/user/custom-roles');\n                if (!response.ok) {\n                    throw new Error('Failed to fetch custom roles');\n                }\n                const roles = await response.json();\n                setCustomRoles(roles);\n            } catch (err) {\n                console.error('Error fetching custom roles:', err);\n                setRolesError(err.message);\n                setCustomRoles([]);\n            } finally{\n                setIsLoadingRoles(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchCustomRoles]\"], []);\n    // Load models and roles on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' || node.type === 'vision' || node.type === 'planner') {\n                fetchModelsFromDatabase();\n            }\n            if (node.type === 'roleAgent') {\n                fetchCustomRoles();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase,\n        fetchCustomRoles\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if ((node.type === 'provider' || node.type === 'vision' || node.type === 'planner') && fetchedProviderModels && fetchedProviderModels.length > 0) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {\n                    let availableModels = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels.length > 0) {\n                        const selectedModelId = availableModels[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                        const currentParams = providerConfig.parameters || {};\n                        // Update config in a single call to avoid infinite loops\n                        const newConfig = {\n                            ...providerConfig,\n                            modelId: selectedModelId,\n                            parameters: {\n                                ...currentParams,\n                                maxTokens: currentParams.maxTokens || reasonableDefault\n                            }\n                        };\n                        setConfig(newConfig);\n                        onUpdate({\n                            config: newConfig,\n                            isConfigured: isNodeConfigured(node.type, newConfig)\n                        });\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        node.type,\n        config === null || config === void 0 ? void 0 : config.providerId\n    ]); // Only re-run when provider changes\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value\n        };\n        // Only initialize parameters if they don't exist and we're setting a parameter\n        if (key === 'parameters' || !currentConfig.parameters) {\n            newConfig.parameters = {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined,\n                ...currentConfig.parameters,\n                ...key === 'parameters' ? value : {}\n            };\n        }\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision')) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // Filter function for vision nodes - only show multimodal models\n                const filterForVision = {\n                    \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (models)=>{\n                        if (node.type === 'vision') {\n                            return models.filter({\n                                \"NodeConfigPanel.useMemo[modelOptions].filterForVision\": (model)=>model.modality && (model.modality.includes('multimodal') || model.modality.includes('vision') || model.modality.includes('image'))\n                            }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"]);\n                        }\n                        return models;\n                    }\n                }[\"NodeConfigPanel.useMemo[modelOptions].filterForVision\"];\n                // If the selected provider is \"OpenRouter\", show all fetched models (filtered for vision if needed)\n                if (currentProviderDetails.id === \"openrouter\") {\n                    const filteredModels = filterForVision(fetchedProviderModels);\n                    return filteredModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    var _deepseekChatModel_modality, _deepseekReasonerModel_modality;\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel && (node.type === 'provider' || node.type === 'vision' && ((_deepseekChatModel_modality = deepseekChatModel.modality) === null || _deepseekChatModel_modality === void 0 ? void 0 : _deepseekChatModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel && (node.type === 'provider' || node.type === 'vision' && ((_deepseekReasonerModel_modality = deepseekReasonerModel.modality) === null || _deepseekReasonerModel_modality === void 0 ? void 0 : _deepseekReasonerModel_modality.includes('multimodal')))) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id and vision capabilities\n                const providerModels = fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions].providerModels\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions].providerModels\"]);\n                const filteredModels = filterForVision(providerModels);\n                return filteredModels.map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider' && node.type !== 'vision') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            return {\n                maxTokens,\n                minTokens\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'vision':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                if (nodeConfig.roleType === 'new') {\n                    return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);\n                }\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'centralRouter':\n                return !!nodeConfig.routingStrategy;\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'planner':\n                return !!(nodeConfig.providerId && nodeConfig.modelId && nodeConfig.apiKey);\n            case 'browsing':\n                return true; // Browsing node is always configured with defaults\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1, _providerConfig_parameters2, _providerConfig_parameters3;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...providerConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters2 = providerConfig.parameters) === null || _providerConfig_parameters2 === void 0 ? void 0 : _providerConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters3 = providerConfig.parameters) === null || _providerConfig_parameters3 === void 0 ? void 0 : _providerConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, this);\n    };\n    const renderVisionConfig = ()=>{\n        var _visionConfig_parameters, _visionConfig_parameters1, _visionConfig_parameters2, _visionConfig_parameters3;\n        const visionConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Vision Model\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-purple-400 ml-1\",\n                                    children: \"(Multimodal Only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...visionConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Vision Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No vision models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 11\n                        }, this),\n                        modelOptions.length === 0 && (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) && !isFetchingProviderModels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg\",\n                            children: \"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Temperature (0.0 - 2.0)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters = visionConfig.parameters) === null || _visionConfig_parameters === void 0 ? void 0 : _visionConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters1 = visionConfig.parameters) === null || _visionConfig_parameters1 === void 0 ? void 0 : _visionConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 720,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters2 = visionConfig.parameters) === null || _visionConfig_parameters2 === void 0 ? void 0 : _visionConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (visionConfig === null || visionConfig === void 0 ? void 0 : (_visionConfig_parameters3 = visionConfig.parameters) === null || _visionConfig_parameters3 === void 0 ? void 0 : _visionConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate for vision analysis.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 770,\n                    columnNumber: 9\n                }, this),\n                (visionConfig === null || visionConfig === void 0 ? void 0 : visionConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 font-medium mb-1\",\n                            children: \"\\uD83D\\uDC41️ Vision Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-purple-200\",\n                            children: \"Access to multimodal models from multiple providers for image analysis and vision tasks.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 839,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 837,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 593,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        const roleConfig = config;\n        // Combine predefined and custom roles for dropdown\n        const availableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>({\n                    id: role.id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'predefined'\n                })),\n            ...customRoles.map((role)=>({\n                    id: role.role_id,\n                    name: role.name,\n                    description: role.description,\n                    type: 'custom'\n                }))\n        ];\n        const handleRoleSelectionChange = (value)=>{\n            if (value === 'create_new') {\n                // Switch to create new role mode\n                const newConfig = {\n                    ...roleConfig,\n                    roleType: 'new',\n                    roleId: '',\n                    roleName: '',\n                    newRoleName: '',\n                    newRoleDescription: '',\n                    customPrompt: ''\n                };\n                setConfig(newConfig);\n                onUpdate({\n                    config: newConfig,\n                    isConfigured: isNodeConfigured(node.type, newConfig)\n                });\n            } else {\n                // Select existing role\n                const selectedRole = availableRoles.find((role)=>role.id === value);\n                if (selectedRole) {\n                    const newConfig = {\n                        ...roleConfig,\n                        roleType: selectedRole.type,\n                        roleId: selectedRole.id,\n                        roleName: selectedRole.name,\n                        customPrompt: selectedRole.description || ''\n                    };\n                    setConfig(newConfig);\n                    onUpdate({\n                        config: newConfig,\n                        isConfigured: isNodeConfigured(node.type, newConfig)\n                    });\n                }\n            }\n        };\n        const handleNewRoleChange = (field, value)=>{\n            const newConfig = {\n                ...roleConfig,\n                [field]: value\n            };\n            setConfig(newConfig);\n            onUpdate({\n                config: newConfig,\n                isConfigured: isNodeConfigured(node.type, newConfig)\n            });\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Select Role\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 11\n                        }, this),\n                        isLoadingRoles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400\",\n                            children: \"Loading roles...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' ? 'create_new' : (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) || '',\n                            onChange: (e)=>handleRoleSelectionChange(e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a role...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"System Roles\",\n                                    children: _config_roles__WEBPACK_IMPORTED_MODULE_3__.PREDEFINED_ROLES.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.id,\n                                            children: role.name\n                                        }, role.id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, this),\n                                customRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Your Custom Roles\",\n                                    children: customRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: role.role_id,\n                                            children: role.name\n                                        }, role.role_id, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                    label: \"Create New\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"create_new\",\n                                        children: \"+ Create New Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 928,\n                            columnNumber: 13\n                        }, this),\n                        rolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error loading roles: \",\n                                rolesError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 9\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) !== 'new' && (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-white mb-1\",\n                            children: roleConfig.roleName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 13\n                        }, this),\n                        roleConfig.customPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300\",\n                            children: roleConfig.customPrompt\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 971,\n                    columnNumber: 11\n                }, this),\n                (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) === 'new' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"New Role Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleName || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleName', e.target.value),\n                                    placeholder: \"e.g., Data Analyst, Creative Writer\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Role Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: roleConfig.newRoleDescription || '',\n                                    onChange: (e)=>handleNewRoleChange('newRoleDescription', e.target.value),\n                                    placeholder: \"Brief description of this role's purpose\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Custom Instructions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1013,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: roleConfig.customPrompt || '',\n                                    onChange: (e)=>handleNewRoleChange('customPrompt', e.target.value),\n                                    placeholder: \"Enter detailed instructions for this role...\",\n                                    rows: 4,\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1012,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.memoryEnabled) || false,\n                                    onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                    className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-300\",\n                                    children: \"Enable memory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1029,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1 ml-6\",\n                            children: \"Allow this role to remember context from previous interactions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1038,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1028,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 917,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1050,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1058,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1053,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1049,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1068,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1067,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1082,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1081,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1094,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1097,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1093,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1080,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1048,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1112,\n            columnNumber: 7\n        }, this);\n    };\n    const renderCentralRouterConfig = ()=>{\n        const routerConfig = config;\n        var _routerConfig_enableCaching, _routerConfig_debugMode;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Routing Strategy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.routingStrategy) || 'smart',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    routingStrategy: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"smart\",\n                                    children: \"Smart Routing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"round_robin\",\n                                    children: \"Round Robin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"load_balanced\",\n                                    children: \"Load Balanced\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"priority\",\n                                    children: \"Priority Based\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"How the router selects between available AI providers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Retries\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"0\",\n                            max: \"10\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.maxRetries) || 3,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    maxRetries: parseInt(e.target.value) || 3\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Number of retry attempts on failure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (ms)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1000\",\n                            max: \"300000\",\n                            step: \"1000\",\n                            value: (routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.timeout) || 30000,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...routerConfig,\n                                    timeout: parseInt(e.target.value) || 30000\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Request timeout in milliseconds\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1201,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Enable Caching\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_enableCaching = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.enableCaching) !== null && _routerConfig_enableCaching !== void 0 ? _routerConfig_enableCaching : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            enableCaching: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Cache responses to improve performance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-300\",\n                                    children: \"Debug Mode\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_routerConfig_debugMode = routerConfig === null || routerConfig === void 0 ? void 0 : routerConfig.debugMode) !== null && _routerConfig_debugMode !== void 0 ? _routerConfig_debugMode : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...routerConfig,\n                                            debugMode: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: \"Enable detailed logging for debugging\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1256,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1144,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolConfig = ()=>{\n        var _toolOptions_find, _toolOptions_find1;\n        const toolConfig = config;\n        const toolOptions = [\n            {\n                value: '',\n                label: 'Select a tool...'\n            },\n            {\n                value: 'google_drive',\n                label: '📁 Google Drive',\n                description: 'Access and manage Google Drive files'\n            },\n            {\n                value: 'google_docs',\n                label: '📄 Google Docs',\n                description: 'Create and edit Google Documents'\n            },\n            {\n                value: 'google_sheets',\n                label: '📊 Google Sheets',\n                description: 'Work with Google Spreadsheets'\n            },\n            {\n                value: 'zapier',\n                label: '⚡ Zapier',\n                description: 'Connect with 5000+ apps via Zapier'\n            },\n            {\n                value: 'notion',\n                label: '📝 Notion',\n                description: 'Access Notion databases and pages'\n            },\n            {\n                value: 'calendar',\n                label: '📅 Calendar',\n                description: 'Manage calendar events and schedules'\n            },\n            {\n                value: 'gmail',\n                label: '📧 Gmail',\n                description: 'Send and manage emails'\n            },\n            {\n                value: 'youtube',\n                label: '📺 YouTube',\n                description: 'Access YouTube data and analytics'\n            },\n            {\n                value: 'supabase',\n                label: '🗄️ Supabase',\n                description: 'Direct database operations'\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Tool Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    toolType: e.target.value,\n                                    // Reset tool-specific config when changing tool type\n                                    toolConfig: {},\n                                    // All tools need authentication\n                                    connectionStatus: 'disconnected',\n                                    isAuthenticated: false\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: toolOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: option.value,\n                                    children: option.label\n                                }, option.value, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1329,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1308,\n                            columnNumber: 11\n                        }, this),\n                        (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: (_toolOptions_find = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find === void 0 ? void 0 : _toolOptions_find.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1304,\n                    columnNumber: 9\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-yellow-400\",\n                                    children: \"Authentication Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mb-2\",\n                                    children: [\n                                        (_toolOptions_find1 = toolOptions.find((opt)=>opt.value === toolConfig.toolType)) === null || _toolOptions_find1 === void 0 ? void 0 : _toolOptions_find1.label,\n                                        \" integration coming soon!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"This tool will require account linking and authentication.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1353,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1349,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1343,\n                    columnNumber: 11\n                }, this),\n                (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.toolType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1363,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"5\",\n                            max: \"300\",\n                            value: (toolConfig === null || toolConfig === void 0 ? void 0 : toolConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...toolConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum time to wait for the tool operation to complete\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1384,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1362,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1303,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPlannerConfig = ()=>{\n        var _plannerConfig_parameters, _plannerConfig_parameters1, _plannerConfig_parameters2, _plannerConfig_parameters3;\n        const plannerConfig = config;\n        // Get available models for the selected provider\n        const getAvailableModels = ()=>{\n            if (!(plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || !fetchedProviderModels) return [];\n            return fetchedProviderModels.filter((model)=>model.provider_id === plannerConfig.providerId);\n        };\n        const availableModels = getAvailableModels();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-purple-900/20 border border-purple-700 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-purple-400\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-purple-400\",\n                                    children: \"Planning Agent Configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-300\",\n                            children: \"Configure the AI model that will create browsing strategies and todo lists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1411,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1406,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"AI Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    apiKey: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) || ''\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1438,\n                                    columnNumber: 13\n                                }, this),\n                                _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: provider.id,\n                                        children: provider.name\n                                    }, provider.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1440,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1421,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1417,\n                    columnNumber: 9\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.providerId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1450,\n                            columnNumber: 13\n                        }, this),\n                        isFetchingProviderModels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 p-3 bg-gray-700 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1455,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"Loading models...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1456,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1454,\n                            columnNumber: 15\n                        }, this) : fetchProviderModelsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-red-900/20 border border-red-700 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-300\",\n                                    children: [\n                                        \"Error loading models: \",\n                                        fetchProviderModelsError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1460,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchModelsFromDatabase,\n                                    className: \"mt-2 text-xs text-red-400 hover:text-red-300 underline\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1461,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1459,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    modelId: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Model...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1484,\n                                    columnNumber: 17\n                                }, this),\n                                availableModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: model.id,\n                                        children: model.display_name || model.name\n                                    }, model.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1486,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1469,\n                            columnNumber: 15\n                        }, this),\n                        availableModels.length === 0 && !isFetchingProviderModels && !fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"No models available for this provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1493,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1449,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.modelId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1503,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    apiKey: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1506,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Your API key for the selected provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1523,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1502,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature: \",\n                                (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters = plannerConfig.parameters) === null || _plannerConfig_parameters === void 0 ? void 0 : _plannerConfig_parameters.temperature) || 0.7\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1532,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"range\",\n                            min: \"0\",\n                            max: \"2\",\n                            step: \"0.1\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters1 = plannerConfig.parameters) === null || _plannerConfig_parameters1 === void 0 ? void 0 : _plannerConfig_parameters1.temperature) || 0.7,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    parameters: {\n                                        ...plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.parameters,\n                                        temperature: parseFloat(e.target.value)\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1535,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Controls randomness in planning (0 = focused, 2 = creative)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1557,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1531,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens: \",\n                                (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters2 = plannerConfig.parameters) === null || _plannerConfig_parameters2 === void 0 ? void 0 : _plannerConfig_parameters2.maxTokens) || 'Auto'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1566,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"range\",\n                            min: tokenLimits.minTokens,\n                            max: tokenLimits.maxTokens,\n                            step: \"100\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : (_plannerConfig_parameters3 = plannerConfig.parameters) === null || _plannerConfig_parameters3 === void 0 ? void 0 : _plannerConfig_parameters3.maxTokens) || tokenLimits.maxTokens,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    parameters: {\n                                        ...plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.parameters,\n                                        maxTokens: parseInt(e.target.value)\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1569,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum tokens for planning response\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1591,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1565,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Subtasks\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1600,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"50\",\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.maxSubtasks) || 10,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    maxSubtasks: parseInt(e.target.value) || 10\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1603,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Maximum number of subtasks the planner can create (1-50)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1621,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1599,\n                    columnNumber: 11\n                }, this),\n                (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.apiKey) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Custom Planning Prompt (Optional)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1630,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: (plannerConfig === null || plannerConfig === void 0 ? void 0 : plannerConfig.planningPrompt) || '',\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...plannerConfig,\n                                    planningPrompt: e.target.value\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            placeholder: \"Enter custom instructions for the planning agent...\",\n                            rows: 3,\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1633,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: \"Additional instructions for how the planner should create browsing strategies\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1650,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1629,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1405,\n            columnNumber: 7\n        }, this);\n    };\n    const renderBrowsingConfig = ()=>{\n        var _browsingConfig_searchEngines, _browsingConfig_searchEngines1;\n        const browsingConfig = config;\n        var _browsingConfig_enableScreenshots, _browsingConfig_enableFormFilling, _browsingConfig_enableCaptchaSolving, _browsingConfig_searchEngines_includes, _browsingConfig_searchEngines_includes1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-green-900/20 border border-green-700 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-400\",\n                                    children: \"●\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-green-400\",\n                                    children: \"Intelligent Browsing Agent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1667,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1665,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-300\",\n                            children: \"This node automatically plans and executes complex web browsing tasks using AI.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1669,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1664,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Max Sites to Visit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1675,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"1\",\n                            max: \"20\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.maxSites) || 5,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    maxSites: parseInt(e.target.value) || 5\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1678,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1674,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Timeout per Operation (seconds)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1699,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            min: \"10\",\n                            max: \"300\",\n                            value: (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.timeout) || 30,\n                            onChange: (e)=>{\n                                const newConfig = {\n                                    ...browsingConfig,\n                                    timeout: parseInt(e.target.value) || 30\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1702,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1698,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300\",\n                            children: \"Capabilities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1723,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableScreenshots = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableScreenshots) !== null && _browsingConfig_enableScreenshots !== void 0 ? _browsingConfig_enableScreenshots : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableScreenshots: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1728,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCF8 Take Screenshots\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1744,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1727,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableFormFilling = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableFormFilling) !== null && _browsingConfig_enableFormFilling !== void 0 ? _browsingConfig_enableFormFilling : true,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableFormFilling: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1748,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDCDD Fill Forms Automatically\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1764,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1747,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: (_browsingConfig_enableCaptchaSolving = browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.enableCaptchaSolving) !== null && _browsingConfig_enableCaptchaSolving !== void 0 ? _browsingConfig_enableCaptchaSolving : false,\n                                    onChange: (e)=>{\n                                        const newConfig = {\n                                            ...browsingConfig,\n                                            enableCaptchaSolving: e.target.checked\n                                        };\n                                        setConfig(newConfig);\n                                        onUpdate({\n                                            config: newConfig,\n                                            isConfigured: isNodeConfigured(node.type, newConfig)\n                                        });\n                                    },\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1768,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"\\uD83D\\uDD10 Attempt CAPTCHA Solving\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1784,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1767,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1722,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Search Engines\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1789,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines === void 0 ? void 0 : _browsingConfig_searchEngines.includes('google')) !== null && _browsingConfig_searchEngines_includes !== void 0 ? _browsingConfig_searchEngines_includes : true,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'google'),\n                                                    'google'\n                                                ] : currentEngines.filter((eng)=>eng !== 'google');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1794,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1815,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1793,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: (_browsingConfig_searchEngines_includes1 = browsingConfig === null || browsingConfig === void 0 ? void 0 : (_browsingConfig_searchEngines1 = browsingConfig.searchEngines) === null || _browsingConfig_searchEngines1 === void 0 ? void 0 : _browsingConfig_searchEngines1.includes('bing')) !== null && _browsingConfig_searchEngines_includes1 !== void 0 ? _browsingConfig_searchEngines_includes1 : false,\n                                            onChange: (e)=>{\n                                                const currentEngines = (browsingConfig === null || browsingConfig === void 0 ? void 0 : browsingConfig.searchEngines) || [\n                                                    'google'\n                                                ];\n                                                const newEngines = e.target.checked ? [\n                                                    ...currentEngines.filter((eng)=>eng !== 'bing'),\n                                                    'bing'\n                                                ] : currentEngines.filter((eng)=>eng !== 'bing');\n                                                const newConfig = {\n                                                    ...browsingConfig,\n                                                    searchEngines: newEngines.length > 0 ? newEngines : [\n                                                        'google'\n                                                    ]\n                                                };\n                                                setConfig(newConfig);\n                                                onUpdate({\n                                                    config: newConfig,\n                                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                                });\n                                            },\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1818,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Bing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 1839,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1817,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1792,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 1788,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 1663,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'vision':\n                return renderVisionConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'centralRouter':\n                return renderCentralRouterConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            case 'tool':\n                return renderToolConfig();\n            case 'planner':\n                return renderPlannerConfig();\n            case 'browsing':\n                return renderBrowsingConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 1876,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1875,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1879,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 1882,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1878,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1874,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 1891,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1887,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1873,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1896,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1903,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 1906,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1902,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 1910,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 1901,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 1871,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"3qPqzANwA5DE2PGqVNbp9Fhx4wo=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});