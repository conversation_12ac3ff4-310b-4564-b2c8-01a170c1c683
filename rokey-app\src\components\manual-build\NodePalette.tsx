'use client';

import { useState } from 'react';
import {
  UserIcon,
  CpuChipIcon,
  CloudIcon,
  EyeIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CodeBracketIcon,
  ArrowPathIcon,
  WrenchScrewdriverIcon,
  CircleStackIcon,
  BoltIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  GlobeAltIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { NodePaletteItem, NodeType } from '@/types/manualBuild';

interface NodePaletteProps {
  onAddNode: (nodeType: string, position: { x: number; y: number }) => void;
}

const nodeCategories = {
  core: {
    label: 'Core Nodes',
    description: 'Essential workflow components',
    nodes: [
      {
        type: 'userRequest' as NodeType,
        label: 'User Request',
        description: 'Starting point for user input',
        icon: UserIcon,
        isAvailable: true,
        defaultData: { label: 'User Request', config: {}, isConfigured: true }
      },
      {
        type: 'classifier' as NodeType,
        label: 'Classifier',
        description: 'Analyzes and categorizes requests',
        icon: CpuChipIcon,
        isAvailable: true,
        defaultData: { label: 'Classifier', config: {}, isConfigured: true }
      },
      {
        type: 'output' as NodeType,
        label: 'Output',
        description: 'Final response to user',
        icon: DocumentTextIcon,
        isAvailable: true,
        defaultData: { label: 'Output', config: {}, isConfigured: true }
      }
    ]
  },
  ai: {
    label: 'AI Providers',
    description: 'AI model integrations',
    nodes: [
      {
        type: 'provider' as NodeType,
        label: 'AI Provider',
        description: 'Connect to AI models (OpenAI, Claude, etc.)',
        icon: CloudIcon,
        isAvailable: true,
        defaultData: {
          label: 'AI Provider',
          config: {
            providerId: '',
            modelId: '',
            apiKey: '',
            parameters: {
              temperature: 1.0,
              maxTokens: undefined,
              topP: undefined,
              frequencyPenalty: undefined,
              presencePenalty: undefined,
            }
          },
          isConfigured: false
        }
      },
      {
        type: 'vision' as NodeType,
        label: 'Vision AI',
        description: 'Multimodal AI for image analysis and vision tasks',
        icon: EyeIcon,
        isAvailable: true,
        defaultData: {
          label: 'Vision AI',
          config: {
            providerId: '',
            modelId: '',
            apiKey: '',
            parameters: {
              temperature: 1.0,
              maxTokens: undefined,
              topP: undefined,
              frequencyPenalty: undefined,
              presencePenalty: undefined,
            }
          },
          isConfigured: false
        }
      },
      {
        type: 'roleAgent' as NodeType,
        label: 'Role Agent',
        description: 'Role plugin for AI providers (connect to role input)',
        icon: UserGroupIcon,
        isAvailable: true,
        defaultData: {
          label: 'Role Agent',
          config: {
            roleId: '',
            roleName: '',
            roleType: 'predefined',
            customPrompt: '',
            memoryEnabled: false
          },
          isConfigured: false
        }
      },
      {
        type: 'centralRouter' as NodeType,
        label: 'Central Router',
        description: 'Smart routing hub for multiple AI providers and vision models',
        icon: CpuChipIcon,
        isAvailable: true,
        defaultData: {
          label: 'Central Router',
          config: {
            routingStrategy: 'smart',
            fallbackProvider: '',
            maxRetries: 3,
            timeout: 30000,
            enableCaching: true,
            debugMode: false
          },
          isConfigured: true
        }
      },
      {
        type: 'planner' as NodeType,
        label: 'Planner',
        description: 'AI model that creates browsing strategies and todo lists',
        icon: ClipboardDocumentListIcon,
        isAvailable: true,
        defaultData: {
          label: 'Planner',
          config: {
            providerId: '',
            modelId: '',
            apiKey: '',
            parameters: {
              temperature: 0.7,
              maxTokens: 1000
            },
            maxSubtasks: 10
          },
          isConfigured: false
        }
      }
    ]
  },
  logic: {
    label: 'Logic & Control',
    description: 'Flow control and decision making',
    nodes: [
      {
        type: 'conditional' as NodeType,
        label: 'Conditional',
        description: 'Branch workflow based on conditions',
        icon: CodeBracketIcon,
        isAvailable: true,
        defaultData: { label: 'Conditional', config: {}, isConfigured: false }
      },
      {
        type: 'merge' as NodeType,
        label: 'Merge',
        description: 'Combine multiple inputs',
        icon: ArrowPathIcon,
        isAvailable: true,
        defaultData: { label: 'Merge', config: {}, isConfigured: true }
      },
      {
        type: 'switch' as NodeType,
        label: 'Switch',
        description: 'Route to different paths',
        icon: BoltIcon,
        isAvailable: true,
        defaultData: { label: 'Switch', config: {}, isConfigured: false }
      },
      {
        type: 'loop' as NodeType,
        label: 'Loop',
        description: 'Repeat operations',
        icon: ArrowPathIcon,
        isAvailable: true,
        defaultData: { label: 'Loop', config: {}, isConfigured: false }
      }
    ]
  },
  tools: {
    label: 'Tools & Integrations',
    description: 'External service integrations',
    nodes: [
      {
        type: 'tool' as NodeType,
        label: 'Tools',
        description: 'External tool integrations (Google Drive, Zapier, etc.)',
        icon: WrenchScrewdriverIcon,
        isAvailable: true,
        defaultData: {
          label: 'Tools',
          config: {
            toolType: '',
            toolConfig: {},
            timeout: 30,
            connectionStatus: 'disconnected',
            isAuthenticated: false
          },
          isConfigured: false
        }
      },
      {
        type: 'memory' as NodeType,
        label: 'Memory',
        description: 'Store and retrieve data across workflow executions',
        icon: CircleStackIcon,
        isAvailable: true,
        defaultData: {
          label: 'Memory',
          config: {
            memoryName: '',
            maxSize: 10240, // 10MB default
            encryption: true, // Enabled by default
            description: ''
          },
          isConfigured: false
        }
      }
    ]
  },
  browsing: {
    label: 'Web Browsing',
    description: 'Intelligent web browsing and automation',
    nodes: [
      {
        type: 'browsing' as NodeType,
        label: 'Browsing Agent',
        description: 'Intelligent web browsing agent with multi-step automation',
        icon: GlobeAltIcon,
        isAvailable: true,
        defaultData: {
          label: 'Browsing Agent',
          config: {
            maxSites: 5,
            timeout: 30,
            enableScreenshots: true,
            enableFormFilling: true,
            enableCaptchaSolving: false,
            searchEngines: ['google'],
            maxDepth: 2,
            respectRobots: true,
            enableJavaScript: true
          },
          isConfigured: true
        }
      }
    ]
  }
};

interface NodeItemProps {
  node: NodePaletteItem;
  onAddNode: (nodeType: string) => void;
}

function NodeItem({ node, onAddNode }: NodeItemProps) {
  const Icon = node.icon;
  
  const handleDragStart = (event: React.DragEvent) => {
    event.dataTransfer.setData('application/reactflow', node.type);
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleClick = () => {
    // Add node at center of canvas
    onAddNode(node.type);
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onClick={handleClick}
      className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
        node.isAvailable
          ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50'
          : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'
      }`}
      title={node.description}
    >
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg ${
          node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'
        }`}>
          <Icon className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className={`font-medium text-sm ${
            node.isAvailable ? 'text-white' : 'text-gray-500'
          }`}>
            {node.label}
          </div>
          <div className="text-xs text-gray-400 truncate">
            {node.description}
          </div>
        </div>
      </div>
    </div>
  );
}

interface CategorySectionProps {
  category: string;
  data: typeof nodeCategories.core;
  isExpanded: boolean;
  onToggle: () => void;
  onAddNode: (nodeType: string) => void;
}

function CategorySection({ category, data, isExpanded, onToggle, onAddNode }: CategorySectionProps) {
  return (
    <div className="mb-4">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors"
      >
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronDownIcon className="w-4 h-4 text-gray-400" />
          ) : (
            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
          )}
          <span className="font-medium text-white">{data.label}</span>
        </div>
        <span className="text-xs text-gray-400">{data.nodes.length}</span>
      </button>
      
      {isExpanded && (
        <div className="mt-2 space-y-2">
          {data.nodes.map((node) => (
            <NodeItem
              key={node.type}
              node={node}
              onAddNode={onAddNode}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default function NodePalette({ onAddNode }: NodePaletteProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['core', 'ai']) // Expand core and AI categories by default
  );

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const handleAddNode = (nodeType: string) => {
    // Add node at a default position (center of canvas)
    onAddNode(nodeType, { x: 400, y: 200 });
  };

  return (
    <div className="w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-white mb-2">Node Palette</h2>
        <p className="text-sm text-gray-400">
          Drag nodes to the canvas or click to add at center
        </p>
      </div>

      <div className="space-y-1">
        {Object.entries(nodeCategories).map(([category, data]) => (
          <CategorySection
            key={category}
            category={category}
            data={data}
            isExpanded={expandedCategories.has(category)}
            onToggle={() => toggleCategory(category)}
            onAddNode={handleAddNode}
          />
        ))}
      </div>

      <div className="mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg">
        <div className="text-sm text-blue-300 font-medium mb-1">💡 Pro Tip</div>
        <div className="text-xs text-blue-200">
          Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.
        </div>
      </div>
    </div>
  );
}
