'use client';

import { CircleStackIcon } from '@heroicons/react/24/outline';
import { NodeProps, useEdges } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, MemoryNodeData } from '@/types/manualBuild';
import { useState, useEffect } from 'react';

export default function MemoryNode({ data, id }: NodeProps<WorkflowNode['data']>) {
  const config = data.config as MemoryNodeData['config'];
  const memoryName = config?.memoryName;
  const maxSize = config?.maxSize || 10240; // 10MB default
  const encryption = config?.encryption !== false; // Default true
  const edges = useEdges();

  // Find connected nodes
  const connectedNodes = edges
    .filter(edge => edge.source === id)
    .map(edge => edge.target);

  const [memoryStats, setMemoryStats] = useState<{
    entriesCount: number;
    totalSize: string;
    lastUpdate: string;
  } | null>(null);

  useEffect(() => {
    // Simulate memory stats - in real implementation, fetch from MemoryService
    if (memoryName) {
      setMemoryStats({
        entriesCount: Math.floor(Math.random() * 50) + 1,
        totalSize: `${(Math.random() * (maxSize / 1024)).toFixed(1)}MB`,
        lastUpdate: new Date().toLocaleTimeString()
      });
    }
  }, [memoryName, maxSize]);

  return (
    <BaseNode
      data={data}
      icon={CircleStackIcon}
      color="#ec4899"
      hasInput={true}
      hasOutput={true}
    >
      <div className="space-y-3">
        {memoryName ? (
          <div className="space-y-2">
            <div className="text-sm font-medium text-white">
              🧠 {memoryName}
            </div>

            <div className="text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded">
              Max: {Math.round(maxSize / 1024)}MB | {encryption ? '🔒 Encrypted' : '🔓 Plain'}
            </div>

            {memoryStats && (
              <div className="text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1">
                <div>📊 {memoryStats.entriesCount} entries</div>
                <div>💾 {memoryStats.totalSize} used</div>
                <div>🕒 Updated {memoryStats.lastUpdate}</div>
              </div>
            )}

            {connectedNodes.length > 0 && (
              <div className="text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded">
                🔗 Connected to {connectedNodes.length} node{connectedNodes.length > 1 ? 's' : ''}
              </div>
            )}

            <div className="text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded">
              ✅ Active & Ready
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              🧠 Plug & Play Memory
            </div>
            <div className="text-xs text-gray-400">
              Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence.
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
