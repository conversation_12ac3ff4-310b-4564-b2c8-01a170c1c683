// Intelligent Browsing Service
// Handles complex multi-step browsing tasks with AI planning and memory

import BrowserlessService from './browserless';
import { memoryService } from './memory/MemoryService';

export interface BrowsingPlan {
  id: string;
  task: string;
  subtasks: BrowsingSubtask[];
  estimatedTime: number;
  priority: 'low' | 'medium' | 'high';
}

export interface BrowsingSubtask {
  id: string;
  type: 'search' | 'navigate' | 'extract' | 'screenshot' | 'form_fill' | 'click' | 'wait' | 'analyze_snippets' | 'check_completion';
  description: string;
  target?: string; // URL, search query, selector, etc.
  parameters?: Record<string, any>;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  result?: any;
  error?: string;
  executionTime?: number;
  priority?: 'low' | 'medium' | 'high'; // For smart selection
}

export interface BrowsingMemory {
  taskId: string;
  completedSubtasks: string[];
  gatheredData: Record<string, any>;
  visitedUrls: string[];
  searchQueries: string[];
  screenshots: string[];
  searchResults: any[]; // Store search snippets for analysis
  selectedWebsites: string[]; // URLs selected based on snippet analysis
  completionStatus: 'insufficient' | 'partial' | 'sufficient' | 'complete';
  lastUpdate: string;
  isComplete: boolean;
  finalResult?: any;
}

export interface BrowsingConfig {
  maxSites?: number;
  timeout?: number;
  enableScreenshots?: boolean;
  enableFormFilling?: boolean;
  enableCaptchaSolving?: boolean;
  searchEngines?: ('google' | 'bing')[];
  maxDepth?: number;
  respectRobots?: boolean;
  userAgent?: string;
  enableJavaScript?: boolean;
}

export class IntelligentBrowsingService {
  private static instance: IntelligentBrowsingService;
  private browserless: BrowserlessService;
  private activeTasks: Map<string, BrowsingMemory> = new Map();
  private memoryNodeId: string | null = null;
  private workflowId: string | null = null;
  private userId: string | null = null;

  constructor() {
    this.browserless = BrowserlessService.getInstance();
  }

  static getInstance(): IntelligentBrowsingService {
    if (!IntelligentBrowsingService.instance) {
      IntelligentBrowsingService.instance = new IntelligentBrowsingService();
    }
    return IntelligentBrowsingService.instance;
  }

  /**
   * Connect to a Memory node for persistent storage
   */
  connectMemory(memoryNodeId: string, workflowId: string, userId: string) {
    this.memoryNodeId = memoryNodeId;
    this.workflowId = workflowId;
    this.userId = userId;
    console.log(`🧠 Browsing connected to Memory node: ${memoryNodeId}`);
  }

  // Execute a browsing plan with memory tracking and smart completion detection
  async executeBrowsingPlan(
    plan: BrowsingPlan,
    memory: BrowsingMemory,
    config: BrowsingConfig = {},
    plannerNodeId?: string,
    aiProviderConfig?: any
  ): Promise<{ memory: BrowsingMemory; result: any }> {
    console.log(`🌐 Starting intelligent browsing task: ${plan.task}`);

    // Load existing memory if connected to Memory node
    if (this.memoryNodeId && this.workflowId && this.userId) {
      const existingMemory = await memoryService.retrieve(
        'browsing_memory',
        this.memoryNodeId,
        this.workflowId,
        this.userId
      );

      if (existingMemory) {
        console.log('🧠 Loaded existing browsing memory');
        // Merge existing memory with current memory
        memory.completedSubtasks = [...(existingMemory.completedSubtasks || []), ...memory.completedSubtasks];
        memory.visitedUrls = [...(existingMemory.visitedUrls || []), ...memory.visitedUrls];
        memory.searchQueries = [...(existingMemory.searchQueries || []), ...memory.searchQueries];
        memory.gatheredData = { ...(existingMemory.gatheredData || {}), ...memory.gatheredData };
        memory.searchResults = [...(existingMemory.searchResults || []), ...memory.searchResults];
        memory.selectedWebsites = [...(existingMemory.selectedWebsites || []), ...memory.selectedWebsites];
        memory.completionStatus = existingMemory.completionStatus || 'insufficient';
      }
    }

    // If we have a planner node, get an enhanced plan
    if (plannerNodeId && aiProviderConfig) {
      console.log('📋 Using planner node to create detailed browsing plan');
      plan = await this.createDetailedPlanWithAI(plan.task, aiProviderConfig, config);
    }

    // Update memory with task start
    memory.lastUpdate = new Date().toISOString();
    memory.completionStatus = 'insufficient';
    this.activeTasks.set(plan.id, memory);

    let finalResult = null;
    const results: any[] = [];

    try {
      for (const subtask of plan.subtasks) {
        // Skip if already completed
        if (memory.completedSubtasks.includes(subtask.id)) {
          console.log(`Skipping completed subtask: ${subtask.description}`);
          continue;
        }

        // Check if we have sufficient information to complete the task early
        if (await this.checkTaskCompletion(memory, plan.task)) {
          console.log(`✅ Task completion detected early. Skipping remaining subtasks.`);
          memory.completionStatus = 'complete';

          // Mark remaining subtasks as skipped
          const remainingSubtasks = plan.subtasks.filter(st =>
            !memory.completedSubtasks.includes(st.id) && st.id !== subtask.id
          );
          remainingSubtasks.forEach(st => st.status = 'skipped');

          break;
        }

        console.log(`Executing subtask: ${subtask.description}`);
        subtask.status = 'in_progress';

        const startTime = Date.now();

        try {
          const result = await this.executeSubtask(subtask, memory, config);

          subtask.status = 'completed';
          subtask.result = result;
          subtask.executionTime = Date.now() - startTime;

          // Update memory
          memory.completedSubtasks.push(subtask.id);
          memory.gatheredData[subtask.id] = result;
          memory.lastUpdate = new Date().toISOString();

          // Update completion status based on gathered data
          memory.completionStatus = this.assessCompletionStatus(memory, plan.task);

          // Save progress to memory after each subtask
          await this.saveMemoryToPersistentStorage(memory);

          results.push(result);

          console.log(`✅ Completed subtask: ${subtask.description} (Status: ${memory.completionStatus})`);

          // Check for early completion
          if (memory.completionStatus === 'sufficient' || memory.completionStatus === 'complete') {
            console.log('🎯 Early completion detected - sufficient information gathered');
            break;
          }

        } catch (error) {
          subtask.status = 'failed';
          subtask.error = error instanceof Error ? error.message : 'Unknown error';
          subtask.executionTime = Date.now() - startTime;

          console.error(`Failed subtask: ${subtask.description}`, error);

          // Decide whether to continue or abort based on subtask importance
          if (subtask.type === 'search' || subtask.type === 'navigate') {
            // Critical subtasks - abort if they fail
            throw error;
          }
          // Non-critical subtasks - continue with warning
        }
      }

      // Synthesize final result from all gathered data
      finalResult = this.synthesizeResults(results, plan.task);

      memory.isComplete = true;
      memory.finalResult = finalResult;
      memory.lastUpdate = new Date().toISOString();

      console.log(`Completed browsing task: ${plan.task}`);

    } catch (error) {
      console.error(`Browsing task failed: ${plan.task}`, error);
      memory.lastUpdate = new Date().toISOString();
      throw error;
    } finally {
      this.activeTasks.set(plan.id, memory);

      // Save memory to Memory node if connected
      if (this.memoryNodeId && this.workflowId && this.userId) {
        await this.saveMemoryToPersistentStorage(memory);
      }
    }

    return { memory, result: finalResult };
  }

  // Execute individual subtask
  private async executeSubtask(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    console.log(`🔄 Executing ${subtask.type} subtask: ${subtask.description}`);

    switch (subtask.type) {
      case 'search':
        return this.executeSearch(subtask, memory, config);
      case 'navigate':
        return this.executeNavigate(subtask, memory, config);
      case 'extract':
        return this.executeExtract(subtask, memory, config);
      case 'screenshot':
        return this.executeScreenshot(subtask, memory, config);
      case 'form_fill':
        return this.executeFormFill(subtask, memory, config);
      case 'click':
        return this.executeClick(subtask, memory, config);
      case 'wait':
        return this.executeWait(subtask, memory, config);
      case 'analyze_snippets':
        return this.executeSnippetAnalysis(subtask, memory, config);
      case 'analyze_results':
        return this.executeResultAnalysis(subtask, memory, config);
      case 'check_completion':
        return this.executeCompletionCheck(subtask, memory, config);
      default:
        throw new Error(`Unknown subtask type: ${subtask.type}`);
    }
  }

  private async executeSearch(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const query = subtask.target || '';
    const searchEngine = config.searchEngines?.[0] || 'google';

    memory.searchQueries.push(query);

    const result = await this.browserless.searchAndExtract(query, searchEngine);

    // Store search results with snippets for analysis
    if (result.data && Array.isArray(result.data)) {
      memory.searchResults.push(...result.data.map((item: any) => ({
        ...item,
        query,
        searchEngine,
        timestamp: new Date().toISOString()
      })));
    }

    return result.data;
  }

  private async executeNavigate(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    console.log('🌐 Navigating to selected websites for detailed extraction');

    // If target is 'selected_websites', use the websites selected from analysis
    let urlsToVisit: string[] = [];

    if (subtask.target === 'selected_websites') {
      urlsToVisit = memory.selectedWebsites || [];
      if (urlsToVisit.length === 0) {
        throw new Error('No websites selected for navigation');
      }
    } else {
      urlsToVisit = [subtask.target || ''];
    }

    const results: any[] = [];
    const selector = subtask.parameters?.selector;

    // Visit each selected website
    for (const url of urlsToVisit) {
      try {
        console.log(`📄 Visiting: ${url}`);
        memory.visitedUrls.push(url);

        const result = await this.browserless.navigateAndExtract(url, selector);

        if (result.data) {
          results.push({
            url,
            content: result.data,
            timestamp: new Date().toISOString(),
            success: true
          });

          // Store individual site data
          memory.gatheredData[url] = result.data;
        }

      } catch (error) {
        console.error(`❌ Failed to visit ${url}:`, error);
        results.push({
          url,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
          success: false
        });
      }
    }

    console.log(`✅ Visited ${results.length} websites, ${results.filter(r => r.success).length} successful`);

    return {
      visitedSites: results,
      successfulVisits: results.filter(r => r.success).length,
      totalAttempts: results.length
    };
  }

  private async executeExtract(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    // Custom extraction logic using Browserless function
    const url = subtask.target || '';
    const selector = subtask.parameters?.selector || 'body';
    
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });
        
        const elements = await page.$$eval("${selector}", els => 
          els.map(el => ({
            text: el.textContent?.trim() || '',
            html: el.innerHTML,
            attributes: Object.fromEntries(
              Array.from(el.attributes).map(attr => [attr.name, attr.value])
            )
          }))
        );
        
        return {
          data: {
            url: "${url}",
            selector: "${selector}",
            elements,
            extractedAt: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    const result = await this.browserless.executeFunction(code);
    return result.data;
  }

  private async executeScreenshot(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const url = subtask.target || '';
    
    const result = await this.browserless.takeScreenshot(url);
    
    if (result.data.screenshot) {
      memory.screenshots.push(result.data.screenshot);
    }
    
    return result.data;
  }

  private async executeFormFill(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    // Form filling logic - to be implemented
    throw new Error('Form filling not yet implemented');
  }

  private async executeClick(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    // Click logic - to be implemented
    throw new Error('Click actions not yet implemented');
  }

  private async executeWait(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const waitTime = subtask.parameters?.duration || 1000;
    await new Promise(resolve => setTimeout(resolve, waitTime));
    return { waited: waitTime };
  }

  // Synthesize final result from all gathered data
  private synthesizeResults(results: any[], originalTask: string): any {
    // Find the memory for this task
    let memory: BrowsingMemory | undefined;
    for (const [taskId, taskMemory] of this.activeTasks.entries()) {
      if (taskMemory.taskId.includes(originalTask.substring(0, 20))) {
        memory = taskMemory;
        break;
      }
    }

    if (!memory) {
      // Fallback if memory not found
      return {
        task: originalTask,
        summary: `Completed browsing task with ${results.length} operations`,
        data: results,
        synthesizedAt: new Date().toISOString(),
        totalOperations: results.length
      };
    }

    // Extract key information from all results
    const searchResults = results.filter(r => r && Array.isArray(r));
    const navigationResults = results.filter(r => r && r.visitedSites);
    const analysisResults = results.filter(r => r && r.selectedWebsites);

    // Compile comprehensive summary
    const summary = {
      task: originalTask,
      status: memory.completionStatus,
      totalOperations: results.length,
      searchesPerformed: memory.searchQueries.length,
      sitesVisited: memory.visitedUrls.length,
      successfulExtractions: Object.keys(memory.gatheredData).length,
      contentQuality: this.assessContentQuality(memory),

      // Key findings
      keyFindings: this.extractKeyFindings(memory, originalTask),

      // Source information
      sources: memory.visitedUrls.map(url => ({
        url,
        hasData: !!memory.gatheredData[url],
        timestamp: memory.lastUpdate
      })),

      // Raw data for further processing
      rawData: memory.gatheredData,

      metadata: {
        executionTime: Date.now() - new Date(memory.lastUpdate).getTime(),
        timestamp: new Date().toISOString(),
        memoryId: memory.taskId
      }
    };

    console.log(`📋 Synthesized final result: ${summary.keyFindings.length} key findings from ${summary.sitesVisited} sites`);

    return summary;
  }

  // Extract key findings from gathered data
  private extractKeyFindings(memory: BrowsingMemory, originalTask: string): string[] {
    const findings: string[] = [];

    // Extract findings from successful site visits
    for (const [url, data] of Object.entries(memory.gatheredData)) {
      if (data && typeof data === 'object') {
        if (data.content && typeof data.content === 'string') {
          // Extract first meaningful sentence or key information
          const content = data.content.substring(0, 200);
          if (content.length > 50) {
            findings.push(`From ${url}: ${content}...`);
          }
        }

        if (data.title) {
          findings.push(`Found: ${data.title} (${url})`);
        }
      }
    }

    // Add search result insights
    if (memory.searchResults.length > 0) {
      const topResult = memory.searchResults[0];
      if (topResult && topResult.title) {
        findings.push(`Top search result: ${topResult.title}`);
      }
    }

    return findings.slice(0, 5); // Limit to top 5 findings
  }

  // Get memory for a task
  getTaskMemory(taskId: string): BrowsingMemory | undefined {
    return this.activeTasks.get(taskId);
  }

  // Smart snippet analysis to select best websites
  private async executeSnippetAnalysis(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    if (memory.searchResults.length === 0) {
      return { selectedWebsites: [], reasoning: 'No search results to analyze' };
    }

    // Analyze snippets and select best websites
    const analysisPrompt = `Analyze these search results and select the 3-5 most relevant websites based on their snippets and titles.

Task: ${subtask.parameters?.originalTask || 'Unknown task'}

Search Results:
${memory.searchResults.map((result, index) =>
  `${index + 1}. Title: ${result.title}
     URL: ${result.url}
     Snippet: ${result.snippet || result.description || 'No snippet available'}
     Source: ${result.query} (${result.searchEngine})`
).join('\n\n')}

Select the best websites that are most likely to contain the information needed. Consider:
- Relevance to the task
- Authority and reliability of the source
- Freshness of content
- Likelihood of containing specific data needed

Respond with JSON:
{
  "selectedUrls": ["url1", "url2", "url3"],
  "reasoning": "explanation of why these sites were chosen",
  "confidence": 0.85
}`;

    try {
      // This would call an AI service to analyze snippets
      // For now, implement a simple heuristic-based selection
      const selectedWebsites = this.selectWebsitesHeuristic(memory.searchResults, subtask);
      memory.selectedWebsites = selectedWebsites;

      return {
        selectedWebsites,
        reasoning: 'Selected based on relevance heuristics',
        confidence: 0.75
      };
    } catch (error) {
      console.error('Snippet analysis failed:', error);
      // Fallback: select first few results
      const fallbackSelection = memory.searchResults.slice(0, 3).map(r => r.url);
      memory.selectedWebsites = fallbackSelection;
      return {
        selectedWebsites: fallbackSelection,
        reasoning: 'Fallback selection due to analysis error',
        confidence: 0.5
      };
    }
  }

  // Execute result analysis to select best websites
  private async executeResultAnalysis(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    console.log('🔍 Analyzing search results to select best websites');

    if (memory.searchResults.length === 0) {
      throw new Error('No search results available for analysis');
    }

    // Analyze and select best websites based on search results
    const selectedWebsites = this.selectWebsitesHeuristic(memory.searchResults, subtask);
    memory.selectedWebsites = selectedWebsites;

    console.log(`📊 Selected ${selectedWebsites.length} websites for detailed browsing`);

    return {
      selectedWebsites,
      totalResults: memory.searchResults.length,
      reasoning: 'Selected based on relevance, authority, and content quality indicators',
      confidence: 0.8
    };
  }

  // Heuristic-based website selection
  private selectWebsitesHeuristic(searchResults: any[], subtask: BrowsingSubtask): string[] {
    const scored = searchResults.map(result => {
      let score = 0;
      const title = (result.title || '').toLowerCase();
      const snippet = (result.snippet || result.description || '').toLowerCase();
      const url = (result.url || '').toLowerCase();

      // Boost official/authoritative sources
      if (url.includes('.gov') || url.includes('.edu') || url.includes('.org')) score += 20;

      // Boost well-known domains
      const knownDomains = ['wikipedia', 'amazon', 'apple', 'google', 'microsoft', 'github'];
      if (knownDomains.some(domain => url.includes(domain))) score += 15;

      // Boost if snippet contains relevant keywords
      const taskKeywords = (subtask.parameters?.keywords || []);
      taskKeywords.forEach((keyword: string) => {
        if (snippet.includes(keyword.toLowerCase())) score += 10;
        if (title.includes(keyword.toLowerCase())) score += 15;
      });

      // Penalize very long URLs (often less reliable)
      if (url.length > 100) score -= 5;

      // Boost if has good snippet (indicates content richness)
      if (snippet.length > 100) score += 5;

      return { ...result, score };
    });

    // Sort by score and take top 3-5
    return scored
      .sort((a, b) => b.score - a.score)
      .slice(0, Math.min(5, config.maxSites || 5))
      .map(r => r.url);
  }

  // Check if task completion criteria are met
  private async executeCompletionCheck(
    subtask: BrowsingSubtask,
    memory: BrowsingMemory,
    config: BrowsingConfig
  ): Promise<any> {
    const completionStatus = this.assessCompletionStatus(memory, subtask.parameters?.originalTask || '');

    return {
      status: completionStatus,
      canComplete: completionStatus === 'sufficient' || completionStatus === 'complete',
      dataPoints: Object.keys(memory.gatheredData).length,
      reasoning: this.getCompletionReasoning(memory, completionStatus)
    };
  }

  // Assess completion status based on gathered data
  private assessCompletionStatus(memory: BrowsingMemory, originalTask: string): 'insufficient' | 'partial' | 'sufficient' | 'complete' {
    const dataPoints = Object.keys(memory.gatheredData).length;
    const hasScreenshots = memory.screenshots.length > 0;
    const visitedSites = memory.visitedUrls.length;
    const successfulVisits = memory.visitedUrls.filter(url => memory.gatheredData[url]).length;
    const searchQueries = memory.searchQueries.length;

    console.log(`📊 Completion assessment: ${dataPoints} data points, ${successfulVisits}/${visitedSites} successful visits, ${searchQueries} searches`);

    // Enhanced completion assessment with content quality
    const contentQuality = this.assessContentQuality(memory);

    if (dataPoints === 0 && searchQueries === 0) return 'insufficient';
    if (dataPoints === 0 && searchQueries > 0) return 'insufficient';
    if (successfulVisits === 0 && dataPoints < 2) return 'partial';
    if (successfulVisits >= 1 && dataPoints >= 2 && contentQuality >= 0.5) return 'sufficient';
    if (successfulVisits >= 3 && dataPoints >= 3 && contentQuality >= 0.7) return 'complete';
    if (dataPoints >= 5 && hasScreenshots && visitedSites >= 3) return 'complete';

    return 'partial';
  }

  // Assess the quality of gathered content
  private assessContentQuality(memory: BrowsingMemory): number {
    let totalScore = 0;
    let scoredItems = 0;

    for (const [key, data] of Object.entries(memory.gatheredData)) {
      if (data && typeof data === 'object') {
        let itemScore = 0;

        // Check for content richness
        if (data.content && typeof data.content === 'string') {
          if (data.content.length > 500) itemScore += 0.3;
          if (data.content.length > 1000) itemScore += 0.2;
        }

        // Check for structured data
        if (data.title) itemScore += 0.1;
        if (data.url) itemScore += 0.1;
        if (data.timestamp) itemScore += 0.1;

        // Check for successful extraction
        if (data.success !== false) itemScore += 0.2;

        totalScore += Math.min(itemScore, 1.0);
        scoredItems++;
      }
    }

    const quality = scoredItems > 0 ? totalScore / scoredItems : 0;
    console.log(`📈 Content quality score: ${quality.toFixed(2)} (${scoredItems} items assessed)`);
    return quality;
  }

  // Check if we have enough information to complete the task early
  private async checkTaskCompletion(memory: BrowsingMemory, originalTask: string): Promise<boolean> {
    const status = this.assessCompletionStatus(memory, originalTask);
    const hasEnoughData = status === 'sufficient' || status === 'complete';

    if (hasEnoughData) {
      console.log(`🎯 Task completion criteria met: ${status}`);

      // Additional intelligent checks
      const intelligentCompletion = await this.performIntelligentCompletionCheck(memory, originalTask);

      if (intelligentCompletion.canComplete) {
        console.log(`🧠 Intelligent completion confirmed: ${intelligentCompletion.reasoning}`);
        return true;
      }
    }

    return false;
  }

  // Perform intelligent completion check using gathered data
  private async performIntelligentCompletionCheck(
    memory: BrowsingMemory,
    originalTask: string
  ): Promise<{ canComplete: boolean; reasoning: string; confidence: number }> {

    const dataPoints = Object.keys(memory.gatheredData).length;
    const successfulVisits = memory.visitedUrls.filter(url => memory.gatheredData[url]).length;
    const contentQuality = this.assessContentQuality(memory);

    // Task-specific completion logic
    const taskLower = originalTask.toLowerCase();

    // For price/comparison tasks
    if (taskLower.includes('price') || taskLower.includes('cost') || taskLower.includes('compare')) {
      if (successfulVisits >= 2 && dataPoints >= 2) {
        return {
          canComplete: true,
          reasoning: 'Found pricing information from multiple sources',
          confidence: 0.85
        };
      }
    }

    // For research/information tasks
    if (taskLower.includes('research') || taskLower.includes('information') || taskLower.includes('about')) {
      if (successfulVisits >= 3 && contentQuality >= 0.6) {
        return {
          canComplete: true,
          reasoning: 'Gathered comprehensive information from multiple authoritative sources',
          confidence: 0.8
        };
      }
    }

    // For specific fact-finding tasks
    if (taskLower.includes('when') || taskLower.includes('what') || taskLower.includes('where')) {
      if (successfulVisits >= 1 && contentQuality >= 0.5) {
        return {
          canComplete: true,
          reasoning: 'Found specific factual information',
          confidence: 0.75
        };
      }
    }

    // General completion criteria
    if (successfulVisits >= 3 && dataPoints >= 3 && contentQuality >= 0.7) {
      return {
        canComplete: true,
        reasoning: 'Sufficient high-quality data gathered from multiple sources',
        confidence: 0.9
      };
    }

    return {
      canComplete: false,
      reasoning: `Need more data: ${successfulVisits} visits, quality ${contentQuality.toFixed(2)}`,
      confidence: 0.3
    };
  }

  // Get reasoning for completion status
  private getCompletionReasoning(memory: BrowsingMemory, status: string): string {
    const dataPoints = Object.keys(memory.gatheredData).length;
    const visitedSites = memory.visitedUrls.length;

    switch (status) {
      case 'insufficient':
        return `Need more data. Currently have ${dataPoints} data points from ${visitedSites} sites.`;
      case 'partial':
        return `Making progress. Have ${dataPoints} data points from ${visitedSites} sites, but need more comprehensive data.`;
      case 'sufficient':
        return `Have enough data to complete task. Collected ${dataPoints} data points from ${visitedSites} sites.`;
      case 'complete':
        return `Task fully complete. Comprehensive data collected: ${dataPoints} data points, ${memory.screenshots.length} screenshots, ${visitedSites} sites visited.`;
      default:
        return 'Status assessment in progress.';
    }
  }

  // Create new memory for a task
  createTaskMemory(taskId: string): BrowsingMemory {
    const memory: BrowsingMemory = {
      taskId,
      completedSubtasks: [],
      gatheredData: {},
      visitedUrls: [],
      searchQueries: [],
      screenshots: [],
      searchResults: [],
      selectedWebsites: [],
      completionStatus: 'insufficient',
      lastUpdate: new Date().toISOString(),
      isComplete: false
    };

    this.activeTasks.set(taskId, memory);
    return memory;
  }

  /**
   * Save browsing memory to persistent storage via Memory node
   */
  private async saveMemoryToPersistentStorage(memory: BrowsingMemory): Promise<void> {
    if (!this.memoryNodeId || !this.workflowId || !this.userId) {
      return;
    }

    try {
      const browsingData = {
        completedSubtasks: memory.completedSubtasks,
        visitedUrls: memory.visitedUrls,
        searchQueries: memory.searchQueries,
        gatheredData: memory.gatheredData,
        currentContext: memory.taskId,
        preferences: {
          searchEngines: memory.searchResults.map(r => r.searchEngine).filter((v, i, a) => a.indexOf(v) === i),
          successfulSites: memory.visitedUrls.filter(url => memory.gatheredData[url]),
          completionStatus: memory.completionStatus
        }
      };

      const success = await memoryService.storeBrowsingMemory(
        'browsing_memory',
        this.memoryNodeId,
        this.workflowId,
        this.userId,
        browsingData,
        {
          memoryName: 'browsing_memory',
          maxSize: 10240, // 10MB
          encryption: true
        }
      );

      if (success) {
        console.log('🧠 Browsing memory saved to persistent storage');
      } else {
        console.warn('⚠️ Failed to save browsing memory');
      }
    } catch (error) {
      console.error('Error saving browsing memory:', error);
    }
  }

  // Get service statistics
  getStats(): any {
    return {
      activeTasks: this.activeTasks.size,
      browserlessStats: this.browserless.getStats(),
      memoryConnected: !!this.memoryNodeId,
      tasks: Array.from(this.activeTasks.values()).map(memory => ({
        taskId: memory.taskId,
        completedSubtasks: memory.completedSubtasks.length,
        isComplete: memory.isComplete,
        lastUpdate: memory.lastUpdate
      }))
    };
  }

  /**
   * Create a detailed browsing plan using AI planner
   */
  private async createDetailedPlanWithAI(
    task: string,
    aiProviderConfig: any,
    browsingConfig: BrowsingConfig
  ): Promise<BrowsingPlan> {
    const maxSubtasks = browsingConfig.maxSites || 5;

    console.log(`📋 Creating AI-enhanced browsing plan for: ${task}`);

    try {
      // Create intelligent plan based on task type
      const plan: BrowsingPlan = {
        id: `ai_plan_${Date.now()}`,
        task,
        subtasks: [
          {
            id: 'search_1',
            type: 'search',
            description: `Primary search for: ${task}`,
            target: task,
            status: 'pending',
            parameters: {
              extractionGoal: 'Find relevant websites and initial information'
            }
          },
          {
            id: 'search_2',
            type: 'search',
            description: `Secondary search for detailed information`,
            target: `${task} detailed information`,
            status: 'pending',
            parameters: {
              extractionGoal: 'Find additional sources and specific details'
            }
          },
          {
            id: 'analyze_1',
            type: 'analyze_results',
            description: 'Analyze search results and select best websites',
            target: 'search_results',
            status: 'pending',
            parameters: {
              extractionGoal: 'Select top 3 most relevant websites based on snippets'
            }
          },
          {
            id: 'navigate_1',
            type: 'navigate',
            description: 'Visit selected websites and extract information',
            target: 'selected_websites',
            status: 'pending',
            parameters: {
              extractionGoal: 'Extract specific information related to the task'
            }
          },
          {
            id: 'completion_check',
            type: 'check_completion',
            description: 'Check if sufficient information has been gathered',
            target: 'gathered_data',
            status: 'pending',
            parameters: {
              extractionGoal: 'Determine if task is complete or needs more information'
            }
          }
        ],
        estimatedTime: Math.min(maxSubtasks * 2, 10), // 2 minutes per subtask, max 10 minutes
        priority: 'medium'
      };

      console.log(`📋 Created AI-enhanced plan with ${plan.subtasks.length} subtasks`);
      return plan;

    } catch (error) {
      console.error('Failed to create AI plan, using fallback:', error);

      // Fallback to simple plan
      return {
        id: `fallback_plan_${Date.now()}`,
        task,
        subtasks: [
          {
            id: 'search_fallback',
            type: 'search',
            description: `Search for: ${task}`,
            target: task,
            status: 'pending'
          }
        ],
        estimatedTime: 5,
        priority: 'medium'
      };
    }
  }
}
