/**
 * Workflow Executor for Manual Build
 * Handles execution of workflows with Memory node integration
 */

import { WorkflowNode } from '@/types/manualBuild';
import { IntelligentBrowsingService } from '../intelligentBrowsing';
import { routerMemoryService } from '../memory/RouterMemoryService';

interface WorkflowExecution {
  id: string;
  userId: string;
  nodes: WorkflowNode[];
  edges: Array<{
    id: string;
    source: string;
    target: string;
    sourceHandle?: string;
    targetHandle?: string;
  }>;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  result?: any;
  error?: string;
}

export class WorkflowExecutor {
  private static instance: WorkflowExecutor;
  private activeExecutions = new Map<string, WorkflowExecution>();

  static getInstance(): WorkflowExecutor {
    if (!WorkflowExecutor.instance) {
      WorkflowExecutor.instance = new WorkflowExecutor();
    }
    return WorkflowExecutor.instance;
  }

  /**
   * Execute a workflow with Memory node integration
   */
  async executeWorkflow(
    workflowId: string,
    userId: string,
    nodes: WorkflowNode[],
    edges: Array<{
      id: string;
      source: string;
      target: string;
      sourceHandle?: string;
      targetHandle?: string;
    }>,
    userInput?: string
  ): Promise<any> {
    console.log(`🚀 Starting workflow execution: ${workflowId}`);

    const execution: WorkflowExecution = {
      id: workflowId,
      userId,
      nodes,
      edges,
      status: 'running',
      startTime: new Date().toISOString()
    };

    this.activeExecutions.set(workflowId, execution);

    try {
      // Step 1: Identify Memory nodes and connect them to other nodes
      await this.connectMemoryNodes(workflowId, userId, nodes, edges);

      // Step 2: Find entry point (User Request node)
      const entryNode = nodes.find(node => node.type === 'userRequest');
      if (!entryNode) {
        throw new Error('No User Request node found in workflow');
      }

      // Step 3: Execute workflow starting from entry point
      const result = await this.executeFromNode(entryNode, nodes, edges, userInput, workflowId, userId);

      execution.status = 'completed';
      execution.endTime = new Date().toISOString();
      execution.result = result;

      console.log(`✅ Workflow completed: ${workflowId}`);
      return result;

    } catch (error) {
      execution.status = 'failed';
      execution.endTime = new Date().toISOString();
      execution.error = error instanceof Error ? error.message : 'Unknown error';

      console.error(`❌ Workflow failed: ${workflowId}`, error);
      throw error;
    }
  }

  /**
   * Connect Memory nodes to their target nodes
   */
  private async connectMemoryNodes(
    workflowId: string,
    userId: string,
    nodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; targetHandle?: string }>
  ): Promise<void> {
    const memoryNodes = nodes.filter(node => node.type === 'memory');

    for (const memoryNode of memoryNodes) {
      // Find nodes that this memory connects to
      const memoryConnections = edges.filter(edge => edge.source === memoryNode.id);

      for (const connection of memoryConnections) {
        const targetNode = nodes.find(node => node.id === connection.target);
        if (!targetNode) continue;

        console.log(`🧠 Connecting Memory "${memoryNode.data.config?.memoryName}" to ${targetNode.type} node`);

        // Connect based on target node type
        switch (targetNode.type) {
          case 'browsing':
            const browsingService = IntelligentBrowsingService.getInstance();
            browsingService.connectMemory(memoryNode.id, workflowId, userId);
            break;

          case 'centralRouter':
            routerMemoryService.connectMemory(memoryNode.id, workflowId, userId);
            break;

          default:
            console.log(`⚠️ Memory connection not implemented for node type: ${targetNode.type}`);
        }
      }
    }
  }

  /**
   * Execute workflow starting from a specific node
   */
  private async executeFromNode(
    currentNode: WorkflowNode,
    allNodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; sourceHandle?: string; targetHandle?: string }>,
    input: any,
    workflowId: string,
    userId: string
  ): Promise<any> {
    console.log(`🔄 Executing node: ${currentNode.type} (${currentNode.id})`);

    let result = input;

    // Execute current node based on its type
    switch (currentNode.type) {
      case 'userRequest':
        result = input; // Pass through user input
        break;

      case 'browsing':
        result = await this.executeBrowsingNode(currentNode, input, workflowId, userId, allNodes, edges);
        break;

      case 'centralRouter':
        result = await this.executeRouterNode(currentNode, input, allNodes, edges);
        break;

      case 'provider':
        result = await this.executeProviderNode(currentNode, input);
        break;

      case 'planner':
        result = await this.executePlannerNode(currentNode, input);
        break;

      case 'memory':
        // Memory nodes don't execute directly - they provide services to other nodes
        result = input;
        break;

      default:
        console.log(`⚠️ Execution not implemented for node type: ${currentNode.type}`);
        result = input;
    }

    // Find next nodes to execute
    const nextEdges = edges.filter(edge => edge.source === currentNode.id);
    
    if (nextEdges.length === 0) {
      // End of workflow
      return result;
    }

    // Execute next nodes (for now, just take the first one - could be enhanced for parallel execution)
    const nextEdge = nextEdges[0];
    const nextNode = allNodes.find(node => node.id === nextEdge.target);
    
    if (nextNode) {
      return await this.executeFromNode(nextNode, allNodes, edges, result, workflowId, userId);
    }

    return result;
  }

  /**
   * Execute a Browsing node
   */
  private async executeBrowsingNode(
    node: WorkflowNode,
    input: any,
    workflowId: string,
    userId: string,
    allNodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; sourceHandle?: string; targetHandle?: string }>
  ): Promise<any> {
    console.log(`🌐 Executing Browsing node: ${node.id}`);

    const browsingService = IntelligentBrowsingService.getInstance();

    // Find connected Planner node
    const plannerConnection = edges.find(edge =>
      edge.target === node.id && edge.targetHandle === 'planner'
    );
    const plannerNode = plannerConnection ?
      allNodes.find(n => n.id === plannerConnection.source) : null;

    // Find connected AI Provider node (for planner execution)
    const aiProviderConnection = edges.find(edge =>
      edge.source === node.id && edge.sourceHandle === 'output'
    );
    const aiProviderNode = aiProviderConnection ?
      allNodes.find(n => n.id === aiProviderConnection.target) : null;

    // Create a browsing plan from the input
    const plan = {
      id: `plan_${Date.now()}`,
      task: typeof input === 'string' ? input : 'Browse the web for information',
      subtasks: [
        {
          id: 'search_1',
          type: 'search' as const,
          description: 'Search for relevant information',
          target: typeof input === 'string' ? input : 'general search',
          status: 'pending' as const
        }
      ],
      estimatedTime: 5,
      priority: 'medium' as const
    };

    // Create memory for this task
    const memory = browsingService.createTaskMemory(plan.id);

    // Execute the browsing plan with planner and AI provider integration
    const { result } = await browsingService.executeBrowsingPlan(
      plan,
      memory,
      node.data.config,
      plannerNode?.id,
      aiProviderNode?.data.config
    );

    console.log(`✅ Browsing node completed: ${node.id}`);
    return result;
  }

  /**
   * Execute a Central Router node
   */
  private async executeRouterNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; targetHandle?: string }>
  ): Promise<any> {
    // Find connected AI providers
    const providerConnections = edges.filter(edge => 
      edge.target === node.id && edge.targetHandle === 'providers'
    );
    
    const availableProviders = providerConnections
      .map(edge => allNodes.find(n => n.id === edge.source))
      .filter(n => n?.type === 'provider')
      .map(n => n!.data.config?.providerId)
      .filter(Boolean);

    if (availableProviders.length === 0) {
      throw new Error('No AI providers connected to router');
    }

    // Get routing recommendation from memory
    const recommendation = routerMemoryService.getRoutingRecommendation(
      typeof input === 'string' ? input : JSON.stringify(input),
      availableProviders
    );

    console.log(`🎯 Router recommendation: ${recommendation.recommendedProvider} (${recommendation.confidence * 100}% confidence)`);
    console.log(`📝 Reason: ${recommendation.reason}`);

    // Record the routing decision
    const startTime = Date.now();
    
    try {
      // For now, just return the recommendation - in a full implementation,
      // this would actually route to the selected provider
      const result = {
        selectedProvider: recommendation.recommendedProvider,
        confidence: recommendation.confidence,
        reason: recommendation.reason,
        input: input
      };

      // Record successful routing
      await routerMemoryService.recordRoutingDecision(
        typeof input === 'string' ? input : JSON.stringify(input),
        recommendation.recommendedProvider,
        recommendation.reason,
        Date.now() - startTime,
        true
      );

      return result;
    } catch (error) {
      // Record failed routing
      await routerMemoryService.recordRoutingDecision(
        typeof input === 'string' ? input : JSON.stringify(input),
        recommendation.recommendedProvider,
        recommendation.reason,
        Date.now() - startTime,
        false
      );
      throw error;
    }
  }

  /**
   * Execute a Provider node
   */
  private async executeProviderNode(node: WorkflowNode, input: any): Promise<any> {
    console.log(`🤖 Executing AI Provider: ${node.data.config?.providerId}`);

    const config = node.data.config;
    const providerId = config?.providerId;
    const modelId = config?.modelId;
    const apiKey = config?.apiKey;

    if (!providerId || !modelId) {
      throw new Error('AI Provider node not properly configured');
    }

    // Process the input based on its type
    let processedInput = input;
    let responseText = '';

    if (typeof input === 'object' && input.visitedSites) {
      // Input from browsing node - synthesize the results
      const sites = input.visitedSites || [];
      const successfulSites = sites.filter((site: any) => site.success);

      if (successfulSites.length === 0) {
        responseText = 'No successful browsing results to process.';
      } else {
        // Create a summary of the browsing results
        const summary = successfulSites.map((site: any) =>
          `Website: ${site.url}\nContent: ${JSON.stringify(site.content).substring(0, 500)}...`
        ).join('\n\n');

        responseText = `Based on browsing ${successfulSites.length} websites, here's what I found:\n\n${summary}`;
      }
    } else if (typeof input === 'object' && input.plan) {
      // Input from planner node
      responseText = `Executed browsing plan: ${input.plan.task}\nCompleted ${input.plan.subtasks?.length || 0} subtasks`;
    } else {
      // Direct text input
      responseText = `Processed request: ${typeof input === 'string' ? input : JSON.stringify(input)}`;
    }

    // For now, return a structured response
    // In a real implementation, this would call the actual AI provider API
    const response = {
      provider: providerId,
      model: modelId,
      response: responseText,
      metadata: {
        inputType: typeof input,
        hasApiKey: !!apiKey,
        timestamp: new Date().toISOString(),
        tokenCount: responseText.length / 4 // Rough estimate
      }
    };

    console.log(`✅ AI Provider response generated: ${responseText.length} characters`);
    return response;
  }

  /**
   * Execute a Planner node
   */
  private async executePlannerNode(node: WorkflowNode, input: any): Promise<any> {
    console.log(`📋 Executing Planner: ${node.data.config?.modelId}`);

    const task = typeof input === 'string' ? input : 'Plan browsing task';
    const maxSubtasks = node.data.config?.maxSubtasks || 5;

    // Create detailed browsing plan
    const plan = {
      id: `planner_${Date.now()}`,
      task,
      subtasks: [
        {
          id: 'search_primary',
          type: 'search',
          description: `Primary search for: ${task}`,
          target: task,
          status: 'pending',
          parameters: {
            extractionGoal: 'Find relevant websites and initial information'
          }
        },
        {
          id: 'search_secondary',
          type: 'search',
          description: `Secondary search for detailed information`,
          target: `${task} detailed information`,
          status: 'pending',
          parameters: {
            extractionGoal: 'Find additional sources and specific details'
          }
        },
        {
          id: 'analyze_results',
          type: 'analyze_results',
          description: 'Analyze search results and select best websites',
          target: 'search_results',
          status: 'pending',
          parameters: {
            extractionGoal: 'Select top websites based on relevance and authority'
          }
        },
        {
          id: 'navigate_selected',
          type: 'navigate',
          description: 'Visit selected websites and extract information',
          target: 'selected_websites',
          status: 'pending',
          parameters: {
            extractionGoal: 'Extract specific information related to the task'
          }
        },
        {
          id: 'completion_check',
          type: 'check_completion',
          description: 'Check if sufficient information has been gathered',
          target: 'gathered_data',
          status: 'pending',
          parameters: {
            extractionGoal: 'Determine if task is complete or needs more information'
          }
        }
      ].slice(0, maxSubtasks),
      estimatedTime: Math.min(maxSubtasks * 2, 10),
      priority: 'medium',
      timestamp: new Date().toISOString()
    };

    console.log(`📋 Created detailed plan with ${plan.subtasks.length} subtasks`);
    return plan;
  }

  /**
   * Get execution status
   */
  getExecutionStatus(workflowId: string): WorkflowExecution | null {
    return this.activeExecutions.get(workflowId) || null;
  }

  /**
   * Get all active executions
   */
  getActiveExecutions(): WorkflowExecution[] {
    return Array.from(this.activeExecutions.values());
  }
}

// Export singleton instance
export const workflowExecutor = WorkflowExecutor.getInstance();
